{"$schema": "https://schema.tauri.app/config/2", "productName": "Kataroma", "version": "2.0.1", "identifier": "com.kataroma.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Kataroma", "width": 800, "height": 600}], "security": {"csp": {"default-src": "'self' customprotocol: asset:", "font-src": ["https://fonts.gstatic.com"], "img-src": "'self' asset: http://asset.localhost blob: data:", "script-src": "'self' 'unsafe-inline' 'unsafe-eval' http://asset.localhost", "connect-src": "'self' https://api.acghost.vip http://127.0.0.1:3000"}, "assetProtocol": {"enable": true, "scope": {"allow": ["$APPDATA/asset/dict/", "$RESOURCE/**"]}}}}, "bundle": {"active": true, "externalBin": ["sidecar/kuroshiro"], "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}