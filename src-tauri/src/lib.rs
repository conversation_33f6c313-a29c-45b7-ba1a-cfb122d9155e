// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
use serde::{Deserialize, Serialize};
use std::fs;
use tauri::AppHandle;
use tauri::Manager;
use tauri::path::BaseDirectory;
use dirs;

// 配置文件结构
#[derive(Deserialize, Debug)]
struct Config {
    api: ApiConfig,
}

#[derive(Deserialize, Debug)]
struct ApiConfig {
    dev_url: String,
    prod_url: String,
}

// Structure for the backend API response
#[derive(Deserialize, Debug)]
struct VipCardInfo {
    value: Option<String>,
    days: Option<u32>,
    charged_at: Option<String>,
}

#[derive(Deserialize, Debug)]
struct ApiResponse {
    message: serde_json::Value, // Can be "ok" or ["兑换码不存在"] etc.
    success: Option<bool>,
    vip_card: Option<VipCardInfo>,
}

// Structure returned by the Tauri command on success
#[derive(Serialize, Debug)]
struct CheckResult {
    is_valid: bool,
    is_used: bool,
    message: String,
    days_to_add: Option<u32>,
}

// 获取Home目录的函数，使用dirs crate作为替代方案
pub fn get_home_dir() -> Option<std::path::PathBuf> {
    // 使用dirs crate的home_dir函数，这不需要应用上下文
    dirs::home_dir()
}

fn get_api_url(_app_handle: AppHandle) -> Result<String, String> {
    // 直接使用内联配置，不再从文件读取
    //#[cfg(debug_assertions)]
    //let api_url = "http://127.0.0.1:3000/kataroma/vip_cards".to_string();

    //#[cfg(not(debug_assertions))]
    let api_url = "https://api.acghost.vip/kataroma/vip_cards".to_string();

    Ok(api_url)
}

#[tauri::command]
async fn check_serial_number(app_handle: AppHandle, serial_number: String) -> Result<CheckResult, String> {
    let client = reqwest::Client::new();

    // 从配置文件获取API URL
    let base_url = get_api_url(app_handle)?;
    let url = format!(
        "{}?id={}&channel=kataroma",
        base_url,
        serial_number
    );

    let response = client.get(&url)
        .header("Content-Type", "application/json")
        .send()
        .await
        .map_err(|e| format!("网络请求失败: {}", e))?;

    if !response.status().is_success() {
        let status = response.status();
        let error_text = response.text().await.unwrap_or_else(|_| "无法读取错误响应体".to_string());
        return Err(format!("服务器错误 (状态码 {}): {}", status, error_text));
    }

    // Clone response data to avoid consuming it before potential re-read on error
    let response_bytes = response.bytes().await.map_err(|e| format!("读取响应体失败: {}", e))?;

    let api_response = serde_json::from_slice::<ApiResponse>(&response_bytes)
      .map_err(|e| {
          let body_string = String::from_utf8_lossy(&response_bytes);
          format!("解析服务器响应失败: {}. 响应体: {}", e, body_string)
      })?;


    let message_str = match api_response.message {
        serde_json::Value::String(s) => s,
        serde_json::Value::Array(arr) => arr.get(0).and_then(|v| v.as_str()).unwrap_or("未知消息").to_string(),
        _ => "未知消息格式".to_string(),
    };

    if message_str == "ok" {
        if let Some(vip_card) = api_response.vip_card {
            if vip_card.charged_at.is_none() {
                let days_to_add = vip_card.days.unwrap_or(30);
                Ok(CheckResult {
                    is_valid: true,
                    is_used: false,
                    message: "ok".to_string(),
                    days_to_add: Some(days_to_add),
                })
            } else {
                Ok(CheckResult {
                    is_valid: true,
                    is_used: true,
                    message: "该序列号已被使用".to_string(),
                    days_to_add: None,
                })
            }
        } else {
             Err("服务器响应格式错误：消息为 'ok' 但缺少 vip_card 信息".to_string())
        }
    } else {
         Ok(CheckResult {
             is_valid: false,
             is_used: false,
             message: message_str,
             days_to_add: None,
         })
    }
}

#[tauri::command]
fn convert_to_romaji(text: String) -> Result<String, String> {
    if text.trim().is_empty() {
        return Ok(String::new());
    }

    let result = kakasi::convert(&text);
    Ok(result.romaji)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_store::Builder::default().build())
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![check_serial_number, convert_to_romaji])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
