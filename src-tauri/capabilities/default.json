{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "main-capability", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "core:path:default", "fs:allow-read", "fs:allow-resource-read-recursive", {"identifier": "shell:allow-execute", "allow": [{"name": "sidecar/kuroshiro", "args": true, "sidecar": true}]}, "store:default", {"identifier": "http:allow-fetch", "allow": [{"url": "https://api.acghost.vip/*"}, {"url": "http://127.0.0.1:3000/*"}]}, {"identifier": "http:allow-fetch-send", "allow": [{"url": "https://api.acghost.vip/*"}, {"url": "http://127.0.0.1:3000/*"}]}, {"identifier": "http:allow-fetch-read-body", "allow": [{"url": "https://api.acghost.vip/*"}, {"url": "http://127.0.0.1:3000/*"}]}]}