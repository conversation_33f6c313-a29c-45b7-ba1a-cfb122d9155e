# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnpm-store

# Build outputs
dist
dist-ssr
*.local

# Tauri specific
src-tauri/target
src-tauri/Cargo.lock
src-tauri/WixTools

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment files
.env
.env.*

# Local Store files
.kataroma.dat
settings.dat
