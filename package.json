{"name": "kataroma_v2", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@iconify/vue": "^4.3.0", "@tauri-apps/plugin-fs": "^2.2.0", "@tauri-apps/plugin-http": "^2.4.3", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.2.0", "@tauri-apps/plugin-store": "^2.2.0", "vue": "^3.5.13", "vue-router": "^4.0.0"}, "devDependencies": {"@iconify-icons/mdi": "^1.2.48", "@iconify/json": "^2.2.328", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-static-copy": "^2.3.1", "vue-tsc": "^2.1.10"}}