<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { Icon } from '@iconify/vue'
import { computed } from 'vue'

const router = useRouter()
const route = useRoute()

const isHome = computed(() => route.path === '/')
const isAbout = computed(() => route.path === '/about')

function navigateToAbout() {
  router.push('/about')
}
</script>

<template>
  <div class="app-container">
    <div class="sidebar">
      <div 
        class="icon" 
        :class="{ active: isHome }"
        @click="router.push('/')"
      >
        <Icon icon="mdi:home" width="24" height="24" />
      </div>
      <div 
        class="icon" 
        :class="{ active: isAbout }"
        @click="navigateToAbout"
      >
        <Icon icon="mdi:information" width="24" height="24" />
      </div>
    </div>
    <div class="main-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<style>
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 60px;
  background-color: #f5f5f5;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon {
  width: 40px;
  height: 40px;
  margin-bottom: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 24px;
}

.icon:hover {
  background-color: #e0e0e0;
}

.icon.active {
  background-color: #396cd8;
  color: white;
}

.icon svg {
  width: 24px;
  height: 24px;
}

.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}
</style>