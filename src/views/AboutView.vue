<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getVersion } from '@tauri-apps/api/app'
import { openUrl } from '@tauri-apps/plugin-opener'
import { settings } from '../config/settings'
import { Store } from '@tauri-apps/plugin-store'
import { formatDate } from '../utils/dateUtils'
import { invoke } from '@tauri-apps/api/core'

// Define the expected result structure from Rust
interface CheckResult {
  is_valid: boolean;
  is_used: boolean;
  message: string;
  days_to_add?: number | null; // Use optional or null
}

const version = ref('')
const isRegistered = ref(false)
const serialNumber = ref('')
const loading = ref(false)
const errorMessage = ref('')
const expireDate = ref('')
let store: Store
// 检查是否为生产环境
const isProd = import.meta.env.PROD

// 格式化到期日期为易读格式
const formattedExpireDate = computed(() => {
  if (!expireDate.value) return ''
  try {
    // 使用格式化函数（如果有）或简单格式化
    return formatDate ? formatDate(new Date(expireDate.value)) : 
      new Date(expireDate.value).toLocaleDateString()
  } catch (e) {
    console.error('日期格式化错误', e)
    return expireDate.value
  }
})

onMounted(async () => {
  console.log('[AboutView] Mounted');
  version.value = await getVersion()
  
  try {
    store = await Store.load('settings.dat')
    console.log('[AboutView] Store loaded successfully.');
    
    // 只在构建时进行一次性清理
    // 通过检查特殊标记来判断是否为首次启动
    if (isProd) {
      const hasInitialized = await store.get('_initialized')
      if (!hasInitialized) {
        // 首次启动，执行清理操作
        console.log('首次启动，清理存储数据')
        // 清除所有数据
        await store.clear()
        // 然后标记为已初始化
        await store.set('_initialized', true)
        await store.save()
      }
    }
  } catch (e) {
    console.error('初始化存储失败', e)
    // 如果加载失败，可以尝试记录错误，但不应回退到相对路径
  }
  
  console.log('[AboutView] Checking registration status on mount...');
  await checkRegistrationStatus()
})

async function checkRegistrationStatus() {
  console.log('[AboutView] Inside checkRegistrationStatus');
  try {
    // Make sure store is initialized before calling get
    if (!store) {
        console.warn('[AboutView] Store not initialized yet in checkRegistrationStatus');
        // Optionally, try loading store again or wait?
        // For now, assume it should be loaded by onMounted
        store = await Store.load('settings.dat');
    }
    const registration = await store.get('registration')
    console.log('[AboutView] Read registration from store:', registration);
    if (registration && typeof registration === 'object' && registration !== null && 'expireDate' in registration) {
      const { expireDate: expireDateStr } = registration as { expireDate: string }
      expireDate.value = expireDateStr
      if (new Date(expireDateStr) > new Date()) {
        isRegistered.value = true
      } else {
        isRegistered.value = false // Ensure it's false if expired
        // Optionally remove expired registration
        // await store.delete('registration');
        // await store.save();
      }
    } else {
      isRegistered.value = false // Ensure it's false if no valid registration data
    }
    console.log('[AboutView] isRegistered status after check:', isRegistered.value);
  } catch (error) {
    console.error('检查注册状态失败:', error)
    isRegistered.value = false // Set to false on error
  }
}

async function openHomepage() {
  await openUrl(settings.homepage_url)
}

async function handleRegister() {
  console.log('[AboutView] handleRegister called');
  if (!serialNumber.value.trim()) {
    errorMessage.value = '请输入序列号'
    return
  }

  errorMessage.value = ''
  console.log('[AboutView] Setting loading to true');
  loading.value = true
  
  try {
    console.log('[AboutView] Calling Rust command check_serial_number with:', serialNumber.value);
    // Use invoke to call the Rust command
    const result = await invoke<CheckResult>('check_serial_number', {
      serialNumber: serialNumber.value // Pass argument as an object field matching Rust function param name
    });
    console.log('[AboutView] Result from Rust:', result);

    if (result.is_valid && !result.is_used) {
      // Success case: Valid and unused serial number
      const daysToAdd = result.days_to_add || 30; // Use default if not provided
      const newExpireDate = new Date();
      newExpireDate.setDate(newExpireDate.getDate() + daysToAdd);
      const expireDateString = newExpireDate.toISOString();

      // Ensure store is initialized before setting
       if (!store) {
            console.error('[AboutView] Store not initialized before saving registration!');
            // Attempt to load store again if necessary
            store = await Store.load('settings.dat');
       }

      await store.set('registration', {
        serialNumber: serialNumber.value, // Save the entered serial number
        expireDate: expireDateString
      });
      await store.save();
      console.log('[AboutView] Registration successful, saved to store');
      isRegistered.value = true;
      expireDate.value = expireDateString;
      errorMessage.value = ''; // Clear any previous error message

    } else {
      // Handle errors reported by the Rust command (invalid code, used code, etc.)
      errorMessage.value = result.message; // Display message from Rust (e.g., "兑换码不存在", "该序列号已被使用")
    }

  } catch (err) {
    // Handle errors from the invoke call itself or errors explicitly returned as Err() from Rust
    console.error('[AboutView] 注册失败 (invoke/Rust error):', err);
    let message = '未知错误';
    if (err instanceof Error) {
      message = err.message;
    } else if (typeof err === 'string') {
      message = err; // Likely the Err(String) from Rust
    }
    errorMessage.value = `注册失败: ${message}`;
  } finally {
    console.log('[AboutView] Entering finally block');
    loading.value = false
    console.log('[AboutView] Set loading to false');
  }
}

// 移除注册信息的函数
async function removeRegistration() {
  try {
    await store.delete('registration')
    await store.save()
    isRegistered.value = false
    expireDate.value = ''
    console.log('已移除注册信息')
  } catch (error) {
    console.error('移除注册信息失败:', error)
  }
}
</script>

<template>
  <div class="about">
    <h2>关于 Kataroma</h2>
    
    <div class="info-section">
      <div class="info-item">
        <span class="label">作者:</span>
        <span>Secwind</span>
      </div>
      
      <div class="info-item">
        <span class="label">版本:</span>
        <span>{{ version }}</span>
      </div>
      
      <div class="info-item">
        <span class="label">发布页:</span>
        <a href="#" @click.prevent="openHomepage">{{ settings.homepage_url }}</a>
      </div>
      
      <div class="info-item">
        <span class="label">注册状态:</span>
        <span :class="{ 'status-registered': isRegistered }">
          {{ isRegistered ? '已注册' : '未注册' }}
        </span>
        <!-- 显示到期日期 -->
        <span v-if="isRegistered && formattedExpireDate" class="expire-date">
          （{{ formattedExpireDate }}）
        </span>
        <!-- 仅在非生产环境且已注册状态下显示移除注册按钮 -->
        <button v-if="!isProd && isRegistered" 
                @click="removeRegistration" 
                class="remove-button">
          移除注册
        </button>
      </div>
    </div>

    <div v-if="!isRegistered" class="register-section">
      <input
        v-model="serialNumber"
        type="text"
        placeholder="请输入序列号"
        :disabled="loading"
      >
      <button 
        @click="handleRegister"
        :disabled="loading || !serialNumber.trim()"
        class="register-button"
      >
        {{ loading ? '注册中...' : '注册' }}
      </button>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<style scoped>
.about {
  padding: 40px;
  text-align: left;
}

h2 {
  margin-bottom: 30px;
  color: #333;
}

.info-section {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
  line-height: 1.6;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.label {
  display: inline-block;
  width: 80px;
  color: #666;
  flex-shrink: 0;
}

a {
  color: #396cd8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.status-registered {
  color: #4caf50;
  font-weight: 500;
  margin-right: 10px;
}

.expire-date {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.register-section {
  margin-top: 20px;
}

input {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
}

button {
  padding: 8px 20px;
  background-color: #396cd8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover:not(:disabled) {
  background-color: #2851b0;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.register-button {
  /* 确保按钮有正确的样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.remove-button {
  margin-left: 10px;
  background-color: #f44336;
  font-size: 12px;
  padding: 4px 10px;
}

.remove-button:hover {
  background-color: #d32f2f;
}

.error-message {
  color: #f44336;
  margin-top: 10px;
}
</style> 