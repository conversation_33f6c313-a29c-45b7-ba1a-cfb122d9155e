<script setup lang="ts">
import { ref, watch } from 'vue'
import { Command } from '@tauri-apps/plugin-shell'

const originalText = ref('')
const resultText = ref('')

async function convertToRomaji() {
  const command = Command.sidecar('sidecar/kuroshiro', [originalText.value])
  const result = await command.execute()
  resultText.value = result.stdout
}

// 监听输入变化
watch(originalText, () => {
  convertToRomaji()
})
</script>

<template>
  <div class="container">
    <div class="text-areas-wrapper">
      <div class="text-area">
        <label>原文</label>
        <textarea
          v-model="originalText"
          placeholder="请输入日文文本..."
        ></textarea>
      </div>
      
      <div class="text-area">
        <label>结果</label>
        <textarea
          v-model="resultText"
          readonly
          placeholder="转换结果将显示在这里..."
        ></textarea>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.text-areas-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

.text-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  font-size: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
}

textarea:focus {
  outline: none;
  border-color: #396cd8;
  box-shadow: 0 2px 8px rgba(57, 108, 216, 0.1);
}

textarea::-webkit-scrollbar {
  width: 8px;
}

textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #999;
}
</style> 