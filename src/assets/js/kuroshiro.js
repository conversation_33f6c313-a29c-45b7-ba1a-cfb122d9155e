(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.<PERSON> = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
module.exports=require("regenerator-runtime");

},{"regenerator-runtime":2}],2:[function(require,module,exports){
var g=function(){return this}()||Function("return this")(),hadRuntime=g.regeneratorRuntime&&Object.getOwnPropertyNames(g).indexOf("regeneratorRuntime")>=0,oldRuntime=hadRuntime&&g.regeneratorRuntime;if(g.regeneratorRuntime=void 0,module.exports=require("./runtime"),hadRuntime)g.regeneratorRuntime=oldRuntime;else try{delete g.regeneratorRuntime}catch(e){g.regeneratorRuntime=void 0}

},{"./runtime":3}],3:[function(require,module,exports){
!function(t){"use strict";var r,e=Object.prototype,n=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag",u="object"==typeof module,h=t.regeneratorRuntime;if(h)u&&(module.exports=h);else{(h=t.regeneratorRuntime=u?module.exports:{}).wrap=w;var s="suspendedStart",f="suspendedYield",l="executing",p="completed",y={},v={};v[i]=function(){return this};var d=Object.getPrototypeOf,g=d&&d(d(P([])));g&&g!==e&&n.call(g,i)&&(v=g);var m=b.prototype=x.prototype=Object.create(v);E.prototype=m.constructor=b,b.constructor=E,b[c]=E.displayName="GeneratorFunction",h.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===E||"GeneratorFunction"===(r.displayName||r.name))},h.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c in t||(t[c]="GeneratorFunction")),t.prototype=Object.create(m),t},h.awrap=function(t){return{__await:t}},_(j.prototype),j.prototype[a]=function(){return this},h.AsyncIterator=j,h.async=function(t,r,e,n){var o=new j(w(t,r,e,n));return h.isGeneratorFunction(r)?o:o.next().then(function(t){return t.done?t.value:o.next()})},_(m),m[c]="Generator",m[i]=function(){return this},m.toString=function(){return"[object Generator]"},h.keys=function(t){var r=[];for(var e in t)r.push(e);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},h.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(G),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return c.type="throw",c.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!h)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),y},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),G(e),y}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;G(e)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:P(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),y}}}function w(t,r,e,n){var o=r&&r.prototype instanceof x?r:x,i=Object.create(o.prototype),a=new N(n||[]);return i._invoke=function(t,r,e){var n=s;return function(o,i){if(n===l)throw new Error("Generator is already running");if(n===p){if("throw"===o)throw i;return F()}for(e.method=o,e.arg=i;;){var a=e.delegate;if(a){var c=O(a,e);if(c){if(c===y)continue;return c}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if(n===s)throw n=p,e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);n=l;var u=L(t,r,e);if("normal"===u.type){if(n=e.done?p:f,u.arg===y)continue;return{value:u.arg,done:e.done}}"throw"===u.type&&(n=p,e.method="throw",e.arg=u.arg)}}}(t,e,a),i}function L(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}function x(){}function E(){}function b(){}function _(t){["next","throw","return"].forEach(function(r){t[r]=function(t){return this._invoke(r,t)}})}function j(t){var r;this._invoke=function(e,o){function i(){return new Promise(function(r,i){!function r(e,o,i,a){var c=L(t[e],t,o);if("throw"!==c.type){var u=c.arg,h=u.value;return h&&"object"==typeof h&&n.call(h,"__await")?Promise.resolve(h.__await).then(function(t){r("next",t,i,a)},function(t){r("throw",t,i,a)}):Promise.resolve(h).then(function(t){u.value=t,i(u)},a)}a(c.arg)}(e,o,r,i)})}return r=r?r.then(i,i):i()}}function O(t,e){var n=t.iterator[e.method];if(n===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=r,O(t,e),"throw"===e.method))return y;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var o=L(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,y;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,y):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,y)}function k(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function G(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function P(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=r,e.done=!0,e};return a.next=a}}return{next:F}}function F(){return{value:r,done:!0}}}(function(){return this}()||Function("return this")());

},{}],4:[function(require,module,exports){
"use strict";var _typeof2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r};Object.defineProperty(exports,"__esModule",{value:!0});var _regenerator=require("babel-runtime/regenerator"),_regenerator2=_interopRequireDefault(_regenerator),_typeof="function"==typeof Symbol&&"symbol"===_typeof2(Symbol.iterator)?function(r){return void 0===r?"undefined":_typeof2(r)}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":void 0===r?"undefined":_typeof2(r)},_createClass=function(){function r(r,a){for(var e=0;e<a.length;e++){var t=a[e];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,t.key,t)}}return function(a,e,t){return e&&r(a.prototype,e),t&&r(a,t),a}}(),_util=require("./util");function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}}function _asyncToGenerator(r){return function(){var a=r.apply(this,arguments);return new Promise(function(r,e){return function t(n,i){try{var o=a[n](i),u=o.value}catch(r){return void e(r)}if(!o.done)return Promise.resolve(u).then(function(r){t("next",r)},function(r){t("throw",r)});r(u)}("next")})}}function _classCallCheck(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")}var Kuroshiro=function(){function r(){_classCallCheck(this,r),this._analyzer=null}return _createClass(r,[{key:"init",value:function(){var r=_asyncToGenerator(_regenerator2.default.mark(function r(a){return _regenerator2.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(a&&"object"===(void 0===a?"undefined":_typeof(a))&&"function"==typeof a.init&&"function"==typeof a.parse){r.next=4;break}throw new Error("Invalid initialization parameter.");case 4:if(null!=this._analyzer){r.next=16;break}return r.prev=5,r.next=8,a.init();case 8:this._analyzer=a,r.next=14;break;case 11:throw r.prev=11,r.t0=r.catch(5),r.t0;case 14:r.next=17;break;case 16:throw new Error("Kuroshiro has already been initialized.");case 17:case"end":return r.stop()}},r,this,[[5,11]])}));return function(a){return r.apply(this,arguments)}}()},{key:"convert",value:function(){var r=_asyncToGenerator(_regenerator2.default.mark(function r(a,e){var t,n,i,o,u,s,f,l,c,_,m,p,d,g,h,y,b,k,w,x,j,v,K,R,S,T,H,E,O,I,M;return _regenerator2.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if((e=e||{}).to=e.to||"hiragana",e.mode=e.mode||"normal",e.romajiSystem=e.romajiSystem||_util.ROMANIZATION_SYSTEM.HEPBURN,e.delimiter_start=e.delimiter_start||"(",e.delimiter_end=e.delimiter_end||")",a=a||"",-1!==["hiragana","katakana","romaji"].indexOf(e.to)){r.next=9;break}throw new Error("Invalid Target Syllabary.");case 9:if(-1!==["normal","spaced","okurigana","furigana"].indexOf(e.mode)){r.next=11;break}throw new Error("Invalid Conversion Mode.");case 11:if(-1!==Object.keys(_util.ROMANIZATION_SYSTEM).map(function(r){return _util.ROMANIZATION_SYSTEM[r]}).indexOf(e.romajiSystem)){r.next=14;break}throw new Error("Invalid Romanization System.");case 14:return r.next=16,this._analyzer.parse(a);case 16:if(t=r.sent,n=(0,_util.patchTokens)(t),"normal"!==e.mode&&"spaced"!==e.mode){r.next=36;break}r.t0=e.to,r.next="katakana"===r.t0?22:"romaji"===r.t0?25:"hiragana"===r.t0?29:33;break;case 22:if("normal"!==e.mode){r.next=24;break}return r.abrupt("return",n.map(function(r){return r.reading}).join(""));case 24:return r.abrupt("return",n.map(function(r){return r.reading}).join(" "));case 25:if(i=function(r){var a=void 0;return a=(0,_util.hasJapanese)(r.surface_form)?r.pronunciation||r.reading:r.surface_form,(0,_util.toRawRomaji)(a,e.romajiSystem)},"normal"!==e.mode){r.next=28;break}return r.abrupt("return",n.map(i).join(""));case 28:return r.abrupt("return",n.map(i).join(" "));case 29:for(o=0;o<n.length;o++)if((0,_util.hasKanji)(n[o].surface_form))if((0,_util.hasKatakana)(n[o].surface_form)){for(n[o].reading=(0,_util.toRawHiragana)(n[o].reading),u="",s="",f=0;f<n[o].surface_form.length;f++)(0,_util.isKanji)(n[o].surface_form[f])?s+="(.*)":s+=(0,_util.isKatakana)(n[o].surface_form[f])?(0,_util.toRawHiragana)(n[o].surface_form[f]):n[o].surface_form[f];if(l=new RegExp(s),c=l.exec(n[o].reading)){for(_=0,m=0;m<n[o].surface_form.length;m++)(0,_util.isKanji)(n[o].surface_form[m])?(u+=c[_+1],_++):u+=n[o].surface_form[m];n[o].reading=u}}else n[o].reading=(0,_util.toRawHiragana)(n[o].reading);else n[o].reading=n[o].surface_form;if("normal"!==e.mode){r.next=32;break}return r.abrupt("return",n.map(function(r){return r.reading}).join(""));case 32:return r.abrupt("return",n.map(function(r){return r.reading}).join(" "));case 33:throw new Error("Unknown option.to param");case 34:r.next=73;break;case 36:if("okurigana"!==e.mode&&"furigana"!==e.mode){r.next=73;break}p=[],d=0;case 39:if(!(d<n.length)){r.next=62;break}g=(0,_util.getStrType)(n[d].surface_form),r.t1=g,r.next=0===r.t1?44:1===r.t1?46:2===r.t1?54:3===r.t1?56:58;break;case 44:return p.push([n[d].surface_form,1,(0,_util.toRawHiragana)(n[d].reading),n[d].pronunciation||n[d].reading]),r.abrupt("break",59);case 46:for(h="",y=!1,b=[],k=0;k<n[d].surface_form.length;k++)(0,_util.isKanji)(n[d].surface_form[k])?y?b[b.length-1]+=n[d].surface_form[k]:(y=!0,h+="(.*)",b.push(n[d].surface_form[k])):(y=!1,b.push(n[d].surface_form[k]),h+=(0,_util.isKatakana)(n[d].surface_form[k])?(0,_util.toRawHiragana)(n[d].surface_form[k]):n[d].surface_form[k]);if(w=new RegExp("^"+h+"$"),x=w.exec((0,_util.toRawHiragana)(n[d].reading)))for(j=1,v=0;v<b.length;v++)(0,_util.isKanji)(b[v][0])?(p.push([b[v],1,x[j],(0,_util.toRawKatakana)(x[j])]),j+=1):p.push([b[v],2,(0,_util.toRawHiragana)(b[v]),(0,_util.toRawKatakana)(b[v])]);else p.push([n[d].surface_form,1,(0,_util.toRawHiragana)(n[d].reading),n[d].pronunciation||n[d].reading]);return r.abrupt("break",59);case 54:for(K=0;K<n[d].surface_form.length;K++)p.push([n[d].surface_form[K],2,(0,_util.toRawHiragana)(n[d].reading[K]),n[d].pronunciation&&n[d].pronunciation[K]||n[d].reading[K]]);return r.abrupt("break",59);case 56:for(R=0;R<n[d].surface_form.length;R++)p.push([n[d].surface_form[R],3,n[d].surface_form[R],n[d].surface_form[R]]);return r.abrupt("break",59);case 58:throw new Error("Unknown strType");case 59:d++,r.next=39;break;case 62:S="",r.t2=e.to,r.next="katakana"===r.t2?66:"romaji"===r.t2?68:"hiragana"===r.t2?70:72;break;case 66:if("okurigana"===e.mode)for(T=0;T<p.length;T++)1!==p[T][1]?S+=p[T][0]:S+=p[T][0]+e.delimiter_start+(0,_util.toRawKatakana)(p[T][2])+e.delimiter_end;else for(H=0;H<p.length;H++)1!==p[H][1]?S+=p[H][0]:S+="<ruby>"+p[H][0]+"<rp>"+e.delimiter_start+"</rp><rt>"+(0,_util.toRawKatakana)(p[H][2])+"</rt><rp>"+e.delimiter_end+"</rp></ruby>";return r.abrupt("return",S);case 68:if("okurigana"===e.mode)for(E=0;E<p.length;E++)1!==p[E][1]?S+=p[E][0]:S+=p[E][0]+e.delimiter_start+(0,_util.toRawRomaji)(p[E][3],e.romajiSystem)+e.delimiter_end;else{for(S+="<ruby>",O=0;O<p.length;O++)S+=p[O][0]+"<rp>"+e.delimiter_start+"</rp><rt>"+(0,_util.toRawRomaji)(p[O][3],e.romajiSystem)+"</rt><rp>"+e.delimiter_end+"</rp>";S+="</ruby>"}return r.abrupt("return",S);case 70:if("okurigana"===e.mode)for(I=0;I<p.length;I++)1!==p[I][1]?S+=p[I][0]:S+=p[I][0]+e.delimiter_start+p[I][2]+e.delimiter_end;else for(M=0;M<p.length;M++)1!==p[M][1]?S+=p[M][0]:S+="<ruby>"+p[M][0]+"<rp>"+e.delimiter_start+"</rp><rt>"+p[M][2]+"</rt><rp>"+e.delimiter_end+"</rp></ruby>";return r.abrupt("return",S);case 72:throw new Error("Invalid Target Syllabary.");case 73:case"end":return r.stop()}},r,this)}));return function(a,e){return r.apply(this,arguments)}}()}]),r}(),Util={isHiragana:_util.isHiragana,isKatakana:_util.isKatakana,isKana:_util.isKana,isKanji:_util.isKanji,isJapanese:_util.isJapanese,hasHiragana:_util.hasHiragana,hasKatakana:_util.hasKatakana,hasKana:_util.hasKana,hasKanji:_util.hasKanji,hasJapanese:_util.hasJapanese,kanaToHiragna:_util.kanaToHiragna,kanaToKatakana:_util.kanaToKatakana,kanaToRomaji:_util.kanaToRomaji};Kuroshiro.Util=Util,exports.default=Kuroshiro,module.exports=exports.default;

},{"./util":6,"babel-runtime/regenerator":1}],5:[function(require,module,exports){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var _core=require("./core"),_core2=_interopRequireDefault(_core);function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}exports.default=_core2.default,module.exports=exports.default;

},{"./core":4}],6:[function(require,module,exports){
"use strict";function _toConsumableArray(a){if(Array.isArray(a)){for(var o=0,e=Array(a.length);o<a.length;o++)e[o]=a[o];return e}return Array.from(a)}Object.defineProperty(exports,"__esModule",{value:!0});var KATAKANA_HIRAGANA_SHIFT="ぁ".charCodeAt(0)-"ァ".charCodeAt(0),HIRAGANA_KATAKANA_SHIFT="ァ".charCodeAt(0)-"ぁ".charCodeAt(0),ROMANIZATION_SYSTEM={NIPPON:"nippon",PASSPORT:"passport",HEPBURN:"hepburn"},isHiragana=function(a){return(a=a[0])>="぀"&&a<="ゟ"},isKatakana=function(a){return(a=a[0])>="゠"&&a<="ヿ"},isKana=function(a){return isHiragana(a)||isKatakana(a)},isKanji=function(a){return(a=a[0])>="一"&&a<="鿏"||a>="豈"&&a<="﫿"||a>="㐀"&&a<="䶿"},isJapanese=function(a){return isKana(a)||isKanji(a)},hasHiragana=function(a){for(var o=0;o<a.length;o++)if(isHiragana(a[o]))return!0;return!1},hasKatakana=function(a){for(var o=0;o<a.length;o++)if(isKatakana(a[o]))return!0;return!1},hasKana=function(a){for(var o=0;o<a.length;o++)if(isKana(a[o]))return!0;return!1},hasKanji=function(a){for(var o=0;o<a.length;o++)if(isKanji(a[o]))return!0;return!1},hasJapanese=function(a){for(var o=0;o<a.length;o++)if(isJapanese(a[o]))return!0;return!1},toRawHiragana=function(a){return[].concat(_toConsumableArray(a)).map(function(a){return a>"゠"&&a<"ヷ"?String.fromCharCode(a.charCodeAt(0)+KATAKANA_HIRAGANA_SHIFT):a}).join("")},toRawKatakana=function(a){return[].concat(_toConsumableArray(a)).map(function(a){return a>"぀"&&a<"゗"?String.fromCharCode(a.charCodeAt(0)+HIRAGANA_KATAKANA_SHIFT):a}).join("")},toRawRomaji=function(a,o){var e={nippon:{"１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","０":"0","！":"!","“":'"',"”":'"',"＃":"#","＄":"$","％":"%","＆":"&","’":"'","（":"(","）":")","＝":"=","～":"~","｜":"|","＠":"@","‘":"`","＋":"+","＊":"*","；":";","：":":","＜":"<","＞":">","、":",","。":".","／":"/","？":"?","＿":"_","・":"･","「":'"',"」":'"',"｛":"{","｝":"}","￥":"\\","＾":"^","あ":"a","い":"i","う":"u","え":"e","お":"o","ア":"a","イ":"i","ウ":"u","エ":"e","オ":"o","か":"ka","き":"ki","く":"ku","け":"ke","こ":"ko","カ":"ka","キ":"ki","ク":"ku","ケ":"ke","コ":"ko","さ":"sa","し":"si","す":"su","せ":"se","そ":"so","サ":"sa","シ":"si","ス":"su","セ":"se","ソ":"so","た":"ta","ち":"ti","つ":"tu","て":"te","と":"to","タ":"ta","チ":"ti","ツ":"tu","テ":"te","ト":"to","な":"na","に":"ni","ぬ":"nu","ね":"ne","の":"no","ナ":"na","ニ":"ni","ヌ":"nu","ネ":"ne","ノ":"no","は":"ha","ひ":"hi","ふ":"hu","へ":"he","ほ":"ho","ハ":"ha","ヒ":"hi","フ":"hu","ヘ":"he","ホ":"ho","ま":"ma","み":"mi","む":"mu","め":"me","も":"mo","マ":"ma","ミ":"mi","ム":"mu","メ":"me","モ":"mo","や":"ya","ゆ":"yu","よ":"yo","ヤ":"ya","ユ":"yu","ヨ":"yo","ら":"ra","り":"ri","る":"ru","れ":"re","ろ":"ro","ラ":"ra","リ":"ri","ル":"ru","レ":"re","ロ":"ro","わ":"wa","ゐ":"wi","ゑ":"we","を":"wo","ワ":"wa","ヰ":"wi","ヱ":"we","ヲ":"wo","が":"ga","ぎ":"gi","ぐ":"gu","げ":"ge","ご":"go","ガ":"ga","ギ":"gi","グ":"gu","ゲ":"ge","ゴ":"go","ざ":"za","じ":"zi","ず":"zu","ぜ":"ze","ぞ":"zo","ザ":"za","ジ":"zi","ズ":"zu","ゼ":"ze","ゾ":"zo","だ":"da","ぢ":"di","づ":"du","で":"de","ど":"do","ダ":"da","ヂ":"di","ヅ":"du","デ":"de","ド":"do","ば":"ba","び":"bi","ぶ":"bu","べ":"be","ぼ":"bo","バ":"ba","ビ":"bi","ブ":"bu","ベ":"be","ボ":"bo","ぱ":"pa","ぴ":"pi","ぷ":"pu","ぺ":"pe","ぽ":"po","パ":"pa","ピ":"pi","プ":"pu","ペ":"pe","ポ":"po","きゃ":"kya","きゅ":"kyu","きょ":"kyo","しゃ":"sya","しゅ":"syu","しょ":"syo","ちゃ":"tya","ちゅ":"tyu","ちょ":"tyo","にゃ":"nya","にゅ":"nyu","にょ":"nyo","ひゃ":"hya","ひゅ":"hyu","ひょ":"hyo","みゃ":"mya","みゅ":"myu","みょ":"myo","りゃ":"rya","りゅ":"ryu","りょ":"ryo","キャ":"kya","キュ":"kyu","キョ":"kyo","シャ":"sya","シュ":"syu","ショ":"syo","チャ":"tya","チュ":"tyu","チョ":"tyo","ニャ":"nya","ニュ":"nyu","ニョ":"nyo","ヒャ":"hya","ヒュ":"hyu","ヒョ":"hyo","ミャ":"mya","ミュ":"myu","ミョ":"myo","リャ":"rya","リュ":"ryu","リョ":"ryo","ぎゃ":"gya","ぎゅ":"gyu","ぎょ":"gyo","じゃ":"zya","じゅ":"zyu","じょ":"zyo","ぢゃ":"dya","ぢゅ":"dyu","ぢょ":"dyo","びゃ":"bya","びゅ":"byu","びょ":"byo","ぴゃ":"pya","ぴゅ":"pyu","ぴょ":"pyo","くゎ":"kwa","ぐゎ":"gwa","ギャ":"gya","ギュ":"gyu","ギョ":"gyo","ジャ":"zya","ジュ":"zyu","ジョ":"zyo","ヂャ":"dya","ヂュ":"dyu","ヂョ":"dyo","ビャ":"bya","ビュ":"byu","ビョ":"byo","ピャ":"pya","ピュ":"pyu","ピョ":"pyo","クヮ":"kwa","グヮ":"gwa","ぁ":"a","ぃ":"i","ぅ":"u","ぇ":"e","ぉ":"o","ゃ":"ya","ゅ":"yu","ょ":"yo","ゎ":"wa","ァ":"a","ィ":"i","ゥ":"u","ェ":"e","ォ":"o","ャ":"ya","ュ":"yu","ョ":"yo","ヮ":"wa","ヵ":"ka","ヶ":"ke","ん":"n","ン":"n","　":" ","いぇ":"ye","きぇ":"kye","くぃ":"kwi","くぇ":"kwe","くぉ":"kwo","ぐぃ":"gwi","ぐぇ":"gwe","ぐぉ":"gwo","イェ":"ye","キェ":"kya","クィ":"kwi","クェ":"kwe","クォ":"kwo","グィ":"gwi","グェ":"gwe","グォ":"gwo","しぇ":"sye","じぇ":"zye","すぃ":"swi","ずぃ":"zwi","ちぇ":"tye","つぁ":"twa","つぃ":"twi","つぇ":"twe","つぉ":"two","にぇ":"nye","ひぇ":"hye","ふぁ":"hwa","ふぃ":"hwi","ふぇ":"hwe","ふぉ":"hwo","ふゅ":"hwyu","ふょ":"hwyo","シェ":"sye","ジェ":"zye","スィ":"swi","ズィ":"zwi","チェ":"tye","ツァ":"twa","ツィ":"twi","ツェ":"twe","ツォ":"two","ニェ":"nye","ヒェ":"hye","ファ":"hwa","フィ":"hwi","フェ":"hwe","フォ":"hwo","フュ":"hwyu","フョ":"hwyo"},passport:{"１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","０":"0","！":"!","“":'"',"”":'"',"＃":"#","＄":"$","％":"%","＆":"&","’":"'","（":"(","）":")","＝":"=","～":"~","｜":"|","＠":"@","‘":"`","＋":"+","＊":"*","；":";","：":":","＜":"<","＞":">","、":",","。":".","／":"/","？":"?","＿":"_","・":"･","「":'"',"」":'"',"｛":"{","｝":"}","￥":"\\","＾":"^","あ":"a","い":"i","う":"u","え":"e","お":"o","ア":"a","イ":"i","ウ":"u","エ":"e","オ":"o","か":"ka","き":"ki","く":"ku","け":"ke","こ":"ko","カ":"ka","キ":"ki","ク":"ku","ケ":"ke","コ":"ko","さ":"sa","し":"shi","す":"su","せ":"se","そ":"so","サ":"sa","シ":"shi","ス":"su","セ":"se","ソ":"so","た":"ta","ち":"chi","つ":"tsu","て":"te","と":"to","タ":"ta","チ":"chi","ツ":"tsu","テ":"te","ト":"to","な":"na","に":"ni","ぬ":"nu","ね":"ne","の":"no","ナ":"na","ニ":"ni","ヌ":"nu","ネ":"ne","ノ":"no","は":"ha","ひ":"hi","ふ":"fu","へ":"he","ほ":"ho","ハ":"ha","ヒ":"hi","フ":"fu","ヘ":"he","ホ":"ho","ま":"ma","み":"mi","む":"mu","め":"me","も":"mo","マ":"ma","ミ":"mi","ム":"mu","メ":"me","モ":"mo","や":"ya","ゆ":"yu","よ":"yo","ヤ":"ya","ユ":"yu","ヨ":"yo","ら":"ra","り":"ri","る":"ru","れ":"re","ろ":"ro","ラ":"ra","リ":"ri","ル":"ru","レ":"re","ロ":"ro","わ":"wa","ゐ":"i","ゑ":"e","を":"o","ワ":"wa","ヰ":"i","ヱ":"e","ヲ":"o","が":"ga","ぎ":"gi","ぐ":"gu","げ":"ge","ご":"go","ガ":"ga","ギ":"gi","グ":"gu","ゲ":"ge","ゴ":"go","ざ":"za","じ":"ji","ず":"zu","ぜ":"ze","ぞ":"zo","ザ":"za","ジ":"ji","ズ":"zu","ゼ":"ze","ゾ":"zo","だ":"da","ぢ":"ji","づ":"zu","で":"de","ど":"do","ダ":"da","ヂ":"ji","ヅ":"zu","デ":"de","ド":"do","ば":"ba","び":"bi","ぶ":"bu","べ":"be","ぼ":"bo","バ":"ba","ビ":"bi","ブ":"bu","ベ":"be","ボ":"bo","ぱ":"pa","ぴ":"pi","ぷ":"pu","ぺ":"pe","ぽ":"po","パ":"pa","ピ":"pi","プ":"pu","ペ":"pe","ポ":"po","きゃ":"kya","きゅ":"kyu","きょ":"kyo","しゃ":"sha","しゅ":"shu","しょ":"sho","ちゃ":"cha","ちゅ":"chu","ちょ":"cho","にゃ":"nya","にゅ":"nyu","にょ":"nyo","ひゃ":"hya","ひゅ":"hyu","ひょ":"hyo","みゃ":"mya","みゅ":"myu","みょ":"myo","りゃ":"rya","りゅ":"ryu","りょ":"ryo","キャ":"kya","キュ":"kyu","キョ":"kyo","シャ":"sha","シュ":"shu","ショ":"sho","チャ":"cha","チュ":"chu","チョ":"cho","ニャ":"nya","ニュ":"nyu","ニョ":"nyo","ヒャ":"hya","ヒュ":"hyu","ヒョ":"hyo","ミャ":"mya","ミュ":"myu","ミョ":"myo","リャ":"rya","リュ":"ryu","リョ":"ryo","ぎゃ":"gya","ぎゅ":"gyu","ぎょ":"gyo","じゃ":"ja","じゅ":"ju","じょ":"jo","ぢゃ":"ja","ぢゅ":"ju","ぢょ":"jo","びゃ":"bya","びゅ":"byu","びょ":"byo","ぴゃ":"pya","ぴゅ":"pyu","ぴょ":"pyo","ギャ":"gya","ギュ":"gyu","ギョ":"gyo","ジャ":"ja","ジュ":"ju","ジョ":"jo","ヂャ":"ja","ヂュ":"ju","ヂョ":"jo","ビャ":"bya","ビュ":"byu","ビョ":"byo","ピャ":"pya","ピュ":"pyu","ピョ":"pyo","ぁ":"a","ぃ":"i","ぅ":"u","ぇ":"e","ぉ":"o","ゃ":"ya","ゅ":"yu","ょ":"yo","ゎ":"wa","ァ":"a","ィ":"i","ゥ":"u","ェ":"e","ォ":"o","ャ":"ya","ュ":"yu","ョ":"yo","ヮ":"wa","ヵ":"ka","ヶ":"ke","ん":"n","ン":"n","　":" ","ヴ":"b"},hepburn:{"１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","０":"0","！":"!","“":'"',"”":'"',"＃":"#","＄":"$","％":"%","＆":"&","’":"'","（":"(","）":")","＝":"=","～":"~","｜":"|","＠":"@","‘":"`","＋":"+","＊":"*","；":";","：":":","＜":"<","＞":">","、":",","。":".","／":"/","？":"?","＿":"_","・":"･","「":'"',"」":'"',"｛":"{","｝":"}","￥":"\\","＾":"^","あ":"a","い":"i","う":"u","え":"e","お":"o","ア":"a","イ":"i","ウ":"u","エ":"e","オ":"o","か":"ka","き":"ki","く":"ku","け":"ke","こ":"ko","カ":"ka","キ":"ki","ク":"ku","ケ":"ke","コ":"ko","さ":"sa","し":"shi","す":"su","せ":"se","そ":"so","サ":"sa","シ":"shi","ス":"su","セ":"se","ソ":"so","た":"ta","ち":"chi","つ":"tsu","て":"te","と":"to","タ":"ta","チ":"chi","ツ":"tsu","テ":"te","ト":"to","な":"na","に":"ni","ぬ":"nu","ね":"ne","の":"no","ナ":"na","ニ":"ni","ヌ":"nu","ネ":"ne","ノ":"no","は":"ha","ひ":"hi","ふ":"fu","へ":"he","ほ":"ho","ハ":"ha","ヒ":"hi","フ":"fu","ヘ":"he","ホ":"ho","ま":"ma","み":"mi","む":"mu","め":"me","も":"mo","マ":"ma","ミ":"mi","ム":"mu","メ":"me","モ":"mo","や":"ya","ゆ":"yu","よ":"yo","ヤ":"ya","ユ":"yu","ヨ":"yo","ら":"ra","り":"ri","る":"ru","れ":"re","ろ":"ro","ラ":"ra","リ":"ri","ル":"ru","レ":"re","ロ":"ro","わ":"wa","ゐ":"i","ゑ":"e","を":"o","ワ":"wa","ヰ":"i","ヱ":"e","ヲ":"o","が":"ga","ぎ":"gi","ぐ":"gu","げ":"ge","ご":"go","ガ":"ga","ギ":"gi","グ":"gu","ゲ":"ge","ゴ":"go","ざ":"za","じ":"ji","ず":"zu","ぜ":"ze","ぞ":"zo","ザ":"za","ジ":"ji","ズ":"zu","ゼ":"ze","ゾ":"zo","だ":"da","ぢ":"ji","づ":"zu","で":"de","ど":"do","ダ":"da","ヂ":"ji","ヅ":"zu","デ":"de","ド":"do","ば":"ba","び":"bi","ぶ":"bu","べ":"be","ぼ":"bo","バ":"ba","ビ":"bi","ブ":"bu","ベ":"be","ボ":"bo","ぱ":"pa","ぴ":"pi","ぷ":"pu","ぺ":"pe","ぽ":"po","パ":"pa","ピ":"pi","プ":"pu","ペ":"pe","ポ":"po","きゃ":"kya","きゅ":"kyu","きょ":"kyo","しゃ":"sha","しゅ":"shu","しょ":"sho","ちゃ":"cha","ちゅ":"chu","ちょ":"cho","にゃ":"nya","にゅ":"nyu","にょ":"nyo","ひゃ":"hya","ひゅ":"hyu","ひょ":"hyo","みゃ":"mya","みゅ":"myu","みょ":"myo","りゃ":"rya","りゅ":"ryu","りょ":"ryo","キャ":"kya","キュ":"kyu","キョ":"kyo","シャ":"sha","シュ":"shu","ショ":"sho","チャ":"cha","チュ":"chu","チョ":"cho","ニャ":"nya","ニュ":"nyu","ニョ":"nyo","ヒャ":"hya","ヒュ":"hyu","ヒョ":"hyo","ミャ":"mya","ミュ":"myu","ミョ":"myo","リャ":"rya","リュ":"ryu","リョ":"ryo","ぎゃ":"gya","ぎゅ":"gyu","ぎょ":"gyo","じゃ":"ja","じゅ":"ju","じょ":"jo","ぢゃ":"ja","ぢゅ":"ju","ぢょ":"jo","びゃ":"bya","びゅ":"byu","びょ":"byo","ぴゃ":"pya","ぴゅ":"pyu","ぴょ":"pyo","ギャ":"gya","ギュ":"gyu","ギョ":"gyo","ジャ":"ja","ジュ":"ju","ジョ":"jo","ヂャ":"ja","ヂュ":"ju","ヂョ":"jo","ビャ":"bya","ビュ":"byu","ビョ":"byo","ピャ":"pya","ピュ":"pyu","ピョ":"pyo","ぁ":"a","ぃ":"i","ぅ":"u","ぇ":"e","ぉ":"o","ゃ":"ya","ゅ":"yu","ょ":"yo","ゎ":"wa","ァ":"a","ィ":"i","ゥ":"u","ェ":"e","ォ":"o","ャ":"ya","ュ":"yu","ョ":"yo","ヮ":"wa","ヵ":"ka","ヶ":"ke","ん":"n","ン":"n","　":" ","いぇ":"ye","うぃ":"wi","うぇ":"we","うぉ":"wo","きぇ":"kye","くぁ":"kwa","くぃ":"kwi","くぇ":"kwe","くぉ":"kwo","ぐぁ":"gwa","ぐぃ":"gwi","ぐぇ":"gwe","ぐぉ":"gwo","イェ":"ye","ウィ":"wi","ウェ":"we","ウォ":"wo","ヴ":"vu","ヴァ":"va","ヴィ":"vi","ヴェ":"ve","ヴォ":"vo","ヴュ":"vyu","ヴョ":"vyo","キェ":"kya","クァ":"kwa","クィ":"kwi","クェ":"kwe","クォ":"kwo","グァ":"gwa","グィ":"gwi","グェ":"gwe","グォ":"gwo","しぇ":"she","じぇ":"je","ちぇ":"che","つぁ":"tsa","つぃ":"tsi","つぇ":"tse","つぉ":"tso","てぃ":"ti","てゅ":"tyu","でぃ":"di","でゅ":"dyu","とぅ":"tu","どぅ":"du","にぇ":"nye","ひぇ":"hye","ふぁ":"fa","ふぃ":"fi","ふぇ":"fe","ふぉ":"fo","ふゅ":"fyu","ふょ":"fyo","シェ":"she","ジェ":"je","チェ":"che","ツァ":"tsa","ツィ":"tsi","ツェ":"tse","ツォ":"tso","ティ":"ti","テュ":"tyu","ディ":"di","デュ":"dyu","トゥ":"tu","ドゥ":"du","ニェ":"nye","ヒェ":"hye","ファ":"fa","フィ":"fi","フェ":"fe","フォ":"fo","フュ":"fyu","フョ":"fyo"}},n=0,r=void 0,u=void 0,i="";if((o=o||ROMANIZATION_SYSTEM.HEPBURN)===ROMANIZATION_SYSTEM.PASSPORT&&(a=a.replace(/ー/gm,"")),o===ROMANIZATION_SYSTEM.NIPPON||o===ROMANIZATION_SYSTEM.HEPBURN){for(var y=new RegExp(/(ん|ン)(?=あ|い|う|え|お|ア|イ|ウ|エ|オ|ぁ|ぃ|ぅ|ぇ|ぉ|ァ|ィ|ゥ|ェ|ォ|や|ゆ|よ|ヤ|ユ|ヨ|ゃ|ゅ|ょ|ャ|ュ|ョ)/g),t=void 0,s=[];null!==(t=y.exec(a));)s.push(t.index+1);if(0!==s.length){for(var h="",g=0;g<s.length;g++)h+=0===g?a.slice(0,s[g])+"'":a.slice(s[g-1],s[g])+"'";a=h+=a.slice(s[s.length-1])}}for(var p=a.length;n<=p;)(u=e[o][a.substring(n,n+2)])?(i+=u,n+=2):(i+=(u=e[o][r=a.substring(n,n+1)])?u:r,n+=1);return i=i.replace(/(っ|ッ)([bcdfghijklmnopqrstuvwyz])/gm,"$2$2"),o!==ROMANIZATION_SYSTEM.PASSPORT&&o!==ROMANIZATION_SYSTEM.HEPBURN||(i=i.replace(/cc/gm,"tc")),i=i.replace(/っ|ッ/gm,"tsu"),o!==ROMANIZATION_SYSTEM.PASSPORT&&o!==ROMANIZATION_SYSTEM.HEPBURN||(i=(i=(i=i.replace(/nm/gm,"mm")).replace(/nb/gm,"mb")).replace(/np/gm,"mp")),o===ROMANIZATION_SYSTEM.NIPPON&&(i=(i=(i=(i=(i=i.replace(/aー/gm,"â")).replace(/iー/gm,"î")).replace(/uー/gm,"û")).replace(/eー/gm,"ê")).replace(/oー/gm,"ô")),o===ROMANIZATION_SYSTEM.HEPBURN&&(i=(i=(i=(i=(i=i.replace(/aー/gm,"ā")).replace(/iー/gm,"ī")).replace(/uー/gm,"ū")).replace(/eー/gm,"ē")).replace(/oー/gm,"ō")),i},getStrType=function(a){for(var o=!1,e=!1,n=0;n<a.length;n++)isKanji(a[n])?o=!0:(isHiragana(a[n])||isKatakana(a[n]))&&(e=!0);return o&&e?1:o?0:e?2:3},patchTokens=function(a){for(var o=0;o<a.length;o++)hasJapanese(a[o].surface_form)?a[o].reading?hasHiragana(a[o].reading)&&(a[o].reading=toRawKatakana(a[o].reading)):a[o].surface_form.split("").every(isKana)?a[o].reading=toRawKatakana(a[o].surface_form):a[o].reading=a[o].surface_form:a[o].reading=a[o].surface_form;for(var e=0;e<a.length;e++)!a[e].pos||"助動詞"!==a[e].pos||"う"!==a[e].surface_form&&"ウ"!==a[e].surface_form||e-1>=0&&a[e-1].pos&&"動詞"===a[e-1].pos&&(a[e-1].surface_form+="う",a[e-1].pronunciation?a[e-1].pronunciation+="ー":a[e-1].pronunciation=a[e-1].reading+"ー",a[e-1].reading+="ウ",a.splice(e,1),e--);for(var n=0;n<a.length;n++)a[n].pos&&("動詞"===a[n].pos||"形容詞"===a[n].pos)&&a[n].surface_form.length>1&&("っ"===a[n].surface_form[a[n].surface_form.length-1]||"ッ"===a[n].surface_form[a[n].surface_form.length-1])&&n+1<a.length&&a[n+1].pos&&("動詞"===a[n+1].pos||"助動詞"===a[n+1].pos)&&(a[n].surface_form+=a[n+1].surface_form,a[n].pronunciation?a[n].pronunciation+=a[n+1].pronunciation:a[n].pronunciation=""+a[n].reading+a[n+1].reading,a[n].reading+=a[n+1].reading,a.splice(n+1,1),n--);return a},kanaToHiragna=function(a){return toRawHiragana(a)},kanaToKatakana=function(a){return toRawKatakana(a)},kanaToRomaji=function(a,o){return toRawRomaji(a,o)};exports.ROMANIZATION_SYSTEM=ROMANIZATION_SYSTEM,exports.getStrType=getStrType,exports.patchTokens=patchTokens,exports.isHiragana=isHiragana,exports.isKatakana=isKatakana,exports.isKana=isKana,exports.isKanji=isKanji,exports.isJapanese=isJapanese,exports.hasHiragana=hasHiragana,exports.hasKatakana=hasKatakana,exports.hasKana=hasKana,exports.hasKanji=hasKanji,exports.hasJapanese=hasJapanese,exports.toRawHiragana=toRawHiragana,exports.toRawKatakana=toRawKatakana,exports.toRawRomaji=toRawRomaji,exports.kanaToHiragna=kanaToHiragna,exports.kanaToKatakana=kanaToKatakana,exports.kanaToRomaji=kanaToRomaji;

},{}]},{},[5])(5)
});

(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.KuromojiAnalyzer = f()}})(function(){var define,module,exports;return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
(function (process,global,setImmediate){
!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(n.async=n.async||{})}(this,function(n){"use strict";function t(n,t){t|=0;for(var r=Math.max(n.length-t,0),e=Array(r),u=0;u<r;u++)e[u]=n[t+u];return e}var r=function(n){var r=t(arguments,1);return function(){var e=t(arguments);return n.apply(null,r.concat(e))}},e=function(n){return function(){var r=t(arguments),e=r.pop();n.call(this,r,e)}};function u(n){var t=typeof n;return null!=n&&("object"==t||"function"==t)}var i="function"==typeof setImmediate&&setImmediate,o="object"==typeof process&&"function"==typeof process.nextTick;function c(n){setTimeout(n,0)}function a(n){return function(r){var e=t(arguments,1);n(function(){r.apply(null,e)})}}var f=a(i?setImmediate:o?process.nextTick:c);function l(n){return e(function(t,r){var e;try{e=n.apply(this,t)}catch(n){return r(n)}u(e)&&"function"==typeof e.then?e.then(function(n){s(r,null,n)},function(n){s(r,n.message?n:new Error(n))}):r(null,e)})}function s(n,t,r){try{n(t,r)}catch(n){f(p,n)}}function p(n){throw n}var v="function"==typeof Symbol;function h(n){return v&&"AsyncFunction"===n[Symbol.toStringTag]}function y(n){return h(n)?l(n):n}function d(n){return function(r){var u=t(arguments,1),i=e(function(t,e){var u=this;return n(r,function(n,r){y(n).apply(u,t.concat(r))},e)});return u.length?i.apply(this,u):i}}var m="object"==typeof global&&global&&global.Object===Object&&global,g="object"==typeof self&&self&&self.Object===Object&&self,b=m||g||Function("return this")(),j=b.Symbol,S=Object.prototype,k=S.hasOwnProperty,L=S.toString,O=j?j.toStringTag:void 0;var w=Object.prototype.toString;var x="[object Null]",E="[object Undefined]",A=j?j.toStringTag:void 0;function T(n){return null==n?void 0===n?E:x:A&&A in Object(n)?function(n){var t=k.call(n,O),r=n[O];try{n[O]=void 0;var e=!0}catch(n){}var u=L.call(n);return e&&(t?n[O]=r:delete n[O]),u}(n):function(n){return w.call(n)}(n)}var B="[object AsyncFunction]",F="[object Function]",I="[object GeneratorFunction]",_="[object Proxy]";var M=9007199254740991;function U(n){return"number"==typeof n&&n>-1&&n%1==0&&n<=M}function q(n){return null!=n&&U(n.length)&&!function(n){if(!u(n))return!1;var t=T(n);return t==F||t==I||t==B||t==_}(n)}var z={};function P(){}function V(n){return function(){if(null!==n){var t=n;n=null,t.apply(this,arguments)}}}var D="function"==typeof Symbol&&Symbol.iterator,R=function(n){return D&&n[D]&&n[D]()};function C(n){return null!=n&&"object"==typeof n}var $="[object Arguments]";function W(n){return C(n)&&T(n)==$}var N=Object.prototype,Q=N.hasOwnProperty,G=N.propertyIsEnumerable,H=W(function(){return arguments}())?W:function(n){return C(n)&&Q.call(n,"callee")&&!G.call(n,"callee")},J=Array.isArray;var K="object"==typeof n&&n&&!n.nodeType&&n,X=K&&"object"==typeof module&&module&&!module.nodeType&&module,Y=X&&X.exports===K?b.Buffer:void 0,Z=(Y?Y.isBuffer:void 0)||function(){return!1},nn=9007199254740991,tn=/^(?:0|[1-9]\d*)$/;function rn(n,t){var r=typeof n;return!!(t=null==t?nn:t)&&("number"==r||"symbol"!=r&&tn.test(n))&&n>-1&&n%1==0&&n<t}var en={};en["[object Float32Array]"]=en["[object Float64Array]"]=en["[object Int8Array]"]=en["[object Int16Array]"]=en["[object Int32Array]"]=en["[object Uint8Array]"]=en["[object Uint8ClampedArray]"]=en["[object Uint16Array]"]=en["[object Uint32Array]"]=!0,en["[object Arguments]"]=en["[object Array]"]=en["[object ArrayBuffer]"]=en["[object Boolean]"]=en["[object DataView]"]=en["[object Date]"]=en["[object Error]"]=en["[object Function]"]=en["[object Map]"]=en["[object Number]"]=en["[object Object]"]=en["[object RegExp]"]=en["[object Set]"]=en["[object String]"]=en["[object WeakMap]"]=!1;var un,on="object"==typeof n&&n&&!n.nodeType&&n,cn=on&&"object"==typeof module&&module&&!module.nodeType&&module,an=cn&&cn.exports===on&&m.process,fn=function(){try{var n=cn&&cn.require&&cn.require("util").types;return n||an&&an.binding&&an.binding("util")}catch(n){}}(),ln=fn&&fn.isTypedArray,sn=ln?(un=ln,function(n){return un(n)}):function(n){return C(n)&&U(n.length)&&!!en[T(n)]},pn=Object.prototype.hasOwnProperty;function vn(n,t){var r=J(n),e=!r&&H(n),u=!r&&!e&&Z(n),i=!r&&!e&&!u&&sn(n),o=r||e||u||i,c=o?function(n,t){for(var r=-1,e=Array(n);++r<n;)e[r]=t(r);return e}(n.length,String):[],a=c.length;for(var f in n)!t&&!pn.call(n,f)||o&&("length"==f||u&&("offset"==f||"parent"==f)||i&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||rn(f,a))||c.push(f);return c}var hn=Object.prototype;var yn=function(n,t){return function(r){return n(t(r))}}(Object.keys,Object),dn=Object.prototype.hasOwnProperty;function mn(n){if(r=(t=n)&&t.constructor,t!==("function"==typeof r&&r.prototype||hn))return yn(n);var t,r,e=[];for(var u in Object(n))dn.call(n,u)&&"constructor"!=u&&e.push(u);return e}function gn(n){return q(n)?vn(n):mn(n)}function bn(n){if(q(n))return function(n){var t=-1,r=n.length;return function(){return++t<r?{value:n[t],key:t}:null}}(n);var t,r,e,u,i=R(n);return i?function(n){var t=-1;return function(){var r=n.next();return r.done?null:(t++,{value:r.value,key:t})}}(i):(r=gn(t=n),e=-1,u=r.length,function(){var n=r[++e];return e<u?{value:t[n],key:n}:null})}function jn(n){return function(){if(null===n)throw new Error("Callback was already called.");var t=n;n=null,t.apply(this,arguments)}}function Sn(n){return function(t,r,e){if(e=V(e||P),n<=0||!t)return e(null);var u=bn(t),i=!1,o=0,c=!1;function a(n,t){if(o-=1,n)i=!0,e(n);else{if(t===z||i&&o<=0)return i=!0,e(null);c||f()}}function f(){for(c=!0;o<n&&!i;){var t=u();if(null===t)return i=!0,void(o<=0&&e(null));o+=1,r(t.value,t.key,jn(a))}c=!1}f()}}function kn(n,t,r,e){Sn(t)(n,y(r),e)}function Ln(n,t){return function(r,e,u){return n(r,t,e,u)}}function On(n,t,r){r=V(r||P);var e=0,u=0,i=n.length;function o(n,t){n?r(n):++u!==i&&t!==z||r(null)}for(0===i&&r(null);e<i;e++)t(n[e],e,jn(o))}var wn=Ln(kn,1/0),xn=function(n,t,r){(q(n)?On:wn)(n,y(t),r)};function En(n){return function(t,r,e){return n(xn,t,y(r),e)}}function An(n,t,r,e){e=e||P,t=t||[];var u=[],i=0,o=y(r);n(t,function(n,t,r){var e=i++;o(n,function(n,t){u[e]=t,r(n)})},function(n){e(n,u)})}var Tn=En(An),Bn=d(Tn);function Fn(n){return function(t,r,e,u){return n(Sn(r),t,y(e),u)}}var In=Fn(An),_n=Ln(In,1),Mn=d(_n);function Un(n,t){for(var r=-1,e=null==n?0:n.length;++r<e&&!1!==t(n[r],r,n););return n}var qn,zn=function(n,t,r){for(var e=-1,u=Object(n),i=r(n),o=i.length;o--;){var c=i[qn?o:++e];if(!1===t(u[c],c,u))break}return n};function Pn(n,t){return n&&zn(n,t,gn)}function Vn(n){return n!=n}function Dn(n,t,r){return t==t?function(n,t,r){for(var e=r-1,u=n.length;++e<u;)if(n[e]===t)return e;return-1}(n,t,r):function(n,t,r,e){for(var u=n.length,i=r+(e?1:-1);e?i--:++i<u;)if(t(n[i],i,n))return i;return-1}(n,Vn,r)}var Rn=function(n,r,e){"function"==typeof r&&(e=r,r=null),e=V(e||P);var u=gn(n).length;if(!u)return e(null);r||(r=u);var i={},o=0,c=!1,a=Object.create(null),f=[],l=[],s={};function p(n,r){f.push(function(){!function(n,r){if(c)return;var u=jn(function(r,u){if(o--,arguments.length>2&&(u=t(arguments,1)),r){var f={};Pn(i,function(n,t){f[t]=n}),f[n]=u,c=!0,a=Object.create(null),e(r,f)}else i[n]=u,Un(a[n]||[],function(n){n()}),v()});o++;var f=y(r[r.length-1]);r.length>1?f(i,u):f(u)}(n,r)})}function v(){if(0===f.length&&0===o)return e(null,i);for(;f.length&&o<r;){f.shift()()}}function h(t){var r=[];return Pn(n,function(n,e){J(n)&&Dn(n,t,0)>=0&&r.push(e)}),r}Pn(n,function(t,r){if(!J(t))return p(r,[t]),void l.push(r);var e=t.slice(0,t.length-1),u=e.length;if(0===u)return p(r,t),void l.push(r);s[r]=u,Un(e,function(i){if(!n[i])throw new Error("async.auto task `"+r+"` has a non-existent dependency `"+i+"` in "+e.join(", "));!function(n,t){var r=a[n];r||(r=a[n]=[]);r.push(t)}(i,function(){0===--u&&p(r,t)})})}),function(){var n,t=0;for(;l.length;)n=l.pop(),t++,Un(h(n),function(n){0==--s[n]&&l.push(n)});if(t!==u)throw new Error("async.auto cannot execute tasks due to a recursive dependency")}(),v()};function Cn(n,t){for(var r=-1,e=null==n?0:n.length,u=Array(e);++r<e;)u[r]=t(n[r],r,n);return u}var $n="[object Symbol]";var Wn=1/0,Nn=j?j.prototype:void 0,Qn=Nn?Nn.toString:void 0;function Gn(n){if("string"==typeof n)return n;if(J(n))return Cn(n,Gn)+"";if(function(n){return"symbol"==typeof n||C(n)&&T(n)==$n}(n))return Qn?Qn.call(n):"";var t=n+"";return"0"==t&&1/n==-Wn?"-0":t}function Hn(n,t,r){var e=n.length;return r=void 0===r?e:r,!t&&r>=e?n:function(n,t,r){var e=-1,u=n.length;t<0&&(t=-t>u?0:u+t),(r=r>u?u:r)<0&&(r+=u),u=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(u);++e<u;)i[e]=n[e+t];return i}(n,t,r)}var Jn=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var Kn="[\\ud800-\\udfff]",Xn="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Yn="\\ud83c[\\udffb-\\udfff]",Zn="[^\\ud800-\\udfff]",nt="(?:\\ud83c[\\udde6-\\uddff]){2}",tt="[\\ud800-\\udbff][\\udc00-\\udfff]",rt="(?:"+Xn+"|"+Yn+")"+"?",et="[\\ufe0e\\ufe0f]?"+rt+("(?:\\u200d(?:"+[Zn,nt,tt].join("|")+")[\\ufe0e\\ufe0f]?"+rt+")*"),ut="(?:"+[Zn+Xn+"?",Xn,nt,tt,Kn].join("|")+")",it=RegExp(Yn+"(?="+Yn+")|"+ut+et,"g");function ot(n){return function(n){return Jn.test(n)}(n)?function(n){return n.match(it)||[]}(n):function(n){return n.split("")}(n)}var ct=/^\s+|\s+$/g;function at(n,t,r){var e;if((n=null==(e=n)?"":Gn(e))&&(r||void 0===t))return n.replace(ct,"");if(!n||!(t=Gn(t)))return n;var u=ot(n),i=ot(t);return Hn(u,function(n,t){for(var r=-1,e=n.length;++r<e&&Dn(t,n[r],0)>-1;);return r}(u,i),function(n,t){for(var r=n.length;r--&&Dn(t,n[r],0)>-1;);return r}(u,i)+1).join("")}var ft=/^(?:async\s+)?(function)?\s*[^\(]*\(\s*([^\)]*)\)/m,lt=/,/,st=/(=.+)?(\s*)$/,pt=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm;function vt(n,t){var r={};Pn(n,function(n,t){var e,u,i=h(n),o=!i&&1===n.length||i&&0===n.length;if(J(n))e=n.slice(0,-1),n=n[n.length-1],r[t]=e.concat(e.length>0?c:n);else if(o)r[t]=n;else{if(e=u=(u=(u=(u=(u=n).toString().replace(pt,"")).match(ft)[2].replace(" ",""))?u.split(lt):[]).map(function(n){return at(n.replace(st,""))}),0===n.length&&!i&&0===e.length)throw new Error("autoInject task functions require explicit parameters.");i||e.pop(),r[t]=e.concat(c)}function c(t,r){var u=Cn(e,function(n){return t[n]});u.push(r),y(n).apply(null,u)}}),Rn(r,t)}function ht(){this.head=this.tail=null,this.length=0}function yt(n,t){n.length=1,n.head=n.tail=t}function dt(n,t,r){if(null==t)t=1;else if(0===t)throw new Error("Concurrency must not be zero");var e=y(n),u=0,i=[],o=!1;function c(n,t,r){if(null!=r&&"function"!=typeof r)throw new Error("task callback must be a function");if(s.started=!0,J(n)||(n=[n]),0===n.length&&s.idle())return f(function(){s.drain()});for(var e=0,u=n.length;e<u;e++){var i={data:n[e],callback:r||P};t?s._tasks.unshift(i):s._tasks.push(i)}o||(o=!0,f(function(){o=!1,s.process()}))}function a(n){return function(t){u-=1;for(var r=0,e=n.length;r<e;r++){var o=n[r],c=Dn(i,o,0);0===c?i.shift():c>0&&i.splice(c,1),o.callback.apply(o,arguments),null!=t&&s.error(t,o.data)}u<=s.concurrency-s.buffer&&s.unsaturated(),s.idle()&&s.drain(),s.process()}}var l=!1,s={_tasks:new ht,concurrency:t,payload:r,saturated:P,unsaturated:P,buffer:t/4,empty:P,drain:P,error:P,started:!1,paused:!1,push:function(n,t){c(n,!1,t)},kill:function(){s.drain=P,s._tasks.empty()},unshift:function(n,t){c(n,!0,t)},remove:function(n){s._tasks.remove(n)},process:function(){if(!l){for(l=!0;!s.paused&&u<s.concurrency&&s._tasks.length;){var n=[],t=[],r=s._tasks.length;s.payload&&(r=Math.min(r,s.payload));for(var o=0;o<r;o++){var c=s._tasks.shift();n.push(c),i.push(c),t.push(c.data)}u+=1,0===s._tasks.length&&s.empty(),u===s.concurrency&&s.saturated();var f=jn(a(n));e(t,f)}l=!1}},length:function(){return s._tasks.length},running:function(){return u},workersList:function(){return i},idle:function(){return s._tasks.length+u===0},pause:function(){s.paused=!0},resume:function(){!1!==s.paused&&(s.paused=!1,f(s.process))}};return s}function mt(n,t){return dt(n,1,t)}ht.prototype.removeLink=function(n){return n.prev?n.prev.next=n.next:this.head=n.next,n.next?n.next.prev=n.prev:this.tail=n.prev,n.prev=n.next=null,this.length-=1,n},ht.prototype.empty=function(){for(;this.head;)this.shift();return this},ht.prototype.insertAfter=function(n,t){t.prev=n,t.next=n.next,n.next?n.next.prev=t:this.tail=t,n.next=t,this.length+=1},ht.prototype.insertBefore=function(n,t){t.prev=n.prev,t.next=n,n.prev?n.prev.next=t:this.head=t,n.prev=t,this.length+=1},ht.prototype.unshift=function(n){this.head?this.insertBefore(this.head,n):yt(this,n)},ht.prototype.push=function(n){this.tail?this.insertAfter(this.tail,n):yt(this,n)},ht.prototype.shift=function(){return this.head&&this.removeLink(this.head)},ht.prototype.pop=function(){return this.tail&&this.removeLink(this.tail)},ht.prototype.toArray=function(){for(var n=Array(this.length),t=this.head,r=0;r<this.length;r++)n[r]=t.data,t=t.next;return n},ht.prototype.remove=function(n){for(var t=this.head;t;){var r=t.next;n(t)&&this.removeLink(t),t=r}return this};var gt=Ln(kn,1);function bt(n,t,r,e){e=V(e||P);var u=y(r);gt(n,function(n,r,e){u(t,n,function(n,r){t=r,e(n)})},function(n){e(n,t)})}function jt(){var n=Cn(arguments,y);return function(){var r=t(arguments),e=this,u=r[r.length-1];"function"==typeof u?r.pop():u=P,bt(n,r,function(n,r,u){r.apply(e,n.concat(function(n){var r=t(arguments,1);u(n,r)}))},function(n,t){u.apply(e,[n].concat(t))})}}var St=function(){return jt.apply(null,t(arguments).reverse())},kt=Array.prototype.concat,Lt=function(n,r,e,u){u=u||P;var i=y(e);In(n,r,function(n,r){i(n,function(n){return n?r(n):r(null,t(arguments,1))})},function(n,t){for(var r=[],e=0;e<t.length;e++)t[e]&&(r=kt.apply(r,t[e]));return u(n,r)})},Ot=Ln(Lt,1/0),wt=Ln(Lt,1),xt=function(){var n=t(arguments),r=[null].concat(n);return function(){return arguments[arguments.length-1].apply(this,r)}};function Et(n){return n}function At(n,t){return function(r,e,u,i){i=i||P;var o,c=!1;r(e,function(r,e,i){u(r,function(e,u){e?i(e):n(u)&&!o?(c=!0,o=t(!0,r),i(null,z)):i()})},function(n){n?i(n):i(null,c?o:t(!1))})}}function Tt(n,t){return t}var Bt=En(At(Et,Tt)),Ft=Fn(At(Et,Tt)),It=Ln(Ft,1);function _t(n){return function(r){var e=t(arguments,1);e.push(function(r){var e=t(arguments,1);"object"==typeof console&&(r?console.error&&console.error(r):console[n]&&Un(e,function(t){console[n](t)}))}),y(r).apply(null,e)}}var Mt=_t("dir");function Ut(n,r,e){e=jn(e||P);var u=y(n),i=y(r);function o(n){if(n)return e(n);var r=t(arguments,1);r.push(c),i.apply(this,r)}function c(n,t){return n?e(n):t?void u(o):e(null)}c(null,!0)}function qt(n,r,e){e=jn(e||P);var u=y(n),i=function(n){if(n)return e(n);var o=t(arguments,1);if(r.apply(this,o))return u(i);e.apply(null,[null].concat(o))};u(i)}function zt(n,t,r){qt(n,function(){return!t.apply(this,arguments)},r)}function Pt(n,t,r){r=jn(r||P);var e=y(t),u=y(n);function i(n){if(n)return r(n);u(o)}function o(n,t){return n?r(n):t?void e(i):r(null)}u(o)}function Vt(n){return function(t,r,e){return n(t,e)}}function Dt(n,t,r){xn(n,Vt(y(t)),r)}function Rt(n,t,r,e){Sn(t)(n,Vt(y(r)),e)}var Ct=Ln(Rt,1);function $t(n){return h(n)?n:e(function(t,r){var e=!0;t.push(function(){var n=arguments;e?f(function(){r.apply(null,n)}):r.apply(null,n)}),n.apply(this,t),e=!1})}function Wt(n){return!n}var Nt=En(At(Wt,Wt)),Qt=Fn(At(Wt,Wt)),Gt=Ln(Qt,1);function Ht(n){return function(t){return null==t?void 0:t[n]}}function Jt(n,t,r,e){var u=new Array(t.length);n(t,function(n,t,e){r(n,function(n,r){u[t]=!!r,e(n)})},function(n){if(n)return e(n);for(var r=[],i=0;i<t.length;i++)u[i]&&r.push(t[i]);e(null,r)})}function Kt(n,t,r,e){var u=[];n(t,function(n,t,e){r(n,function(r,i){r?e(r):(i&&u.push({index:t,value:n}),e())})},function(n){n?e(n):e(null,Cn(u.sort(function(n,t){return n.index-t.index}),Ht("value")))})}function Xt(n,t,r,e){(q(t)?Jt:Kt)(n,t,y(r),e||P)}var Yt=En(Xt),Zt=Fn(Xt),nr=Ln(Zt,1);function tr(n,t){var r=jn(t||P),e=y($t(n));!function n(t){if(t)return r(t);e(n)}()}var rr=function(n,t,r,e){e=e||P;var u=y(r);In(n,t,function(n,t){u(n,function(r,e){return r?t(r):t(null,{key:e,val:n})})},function(n,t){for(var r={},u=Object.prototype.hasOwnProperty,i=0;i<t.length;i++)if(t[i]){var o=t[i].key,c=t[i].val;u.call(r,o)?r[o].push(c):r[o]=[c]}return e(n,r)})},er=Ln(rr,1/0),ur=Ln(rr,1),ir=_t("log");function or(n,t,r,e){e=V(e||P);var u={},i=y(r);kn(n,t,function(n,t,r){i(n,t,function(n,e){if(n)return r(n);u[t]=e,r()})},function(n){e(n,u)})}var cr=Ln(or,1/0),ar=Ln(or,1);function fr(n,t){return t in n}function lr(n,r){var u=Object.create(null),i=Object.create(null);r=r||Et;var o=y(n),c=e(function(n,e){var c=r.apply(null,n);fr(u,c)?f(function(){e.apply(null,u[c])}):fr(i,c)?i[c].push(e):(i[c]=[e],o.apply(null,n.concat(function(){var n=t(arguments);u[c]=n;var r=i[c];delete i[c];for(var e=0,o=r.length;e<o;e++)r[e].apply(null,n)})))});return c.memo=u,c.unmemoized=n,c}var sr=a(o?process.nextTick:i?setImmediate:c);function pr(n,r,e){e=e||P;var u=q(r)?[]:{};n(r,function(n,r,e){y(n)(function(n,i){arguments.length>2&&(i=t(arguments,1)),u[r]=i,e(n)})},function(n){e(n,u)})}function vr(n,t){pr(xn,n,t)}function hr(n,t,r){pr(Sn(t),n,r)}var yr=function(n,t){var r=y(n);return dt(function(n,t){r(n[0],t)},t,1)},dr=function(n,t){var r=yr(n,t);return r.push=function(n,t,e){if(null==e&&(e=P),"function"!=typeof e)throw new Error("task callback must be a function");if(r.started=!0,J(n)||(n=[n]),0===n.length)return f(function(){r.drain()});t=t||0;for(var u=r._tasks.head;u&&t>=u.priority;)u=u.next;for(var i=0,o=n.length;i<o;i++){var c={data:n[i],priority:t,callback:e};u?r._tasks.insertBefore(u,c):r._tasks.push(c)}f(r.process)},delete r.unshift,r};function mr(n,t){if(t=V(t||P),!J(n))return t(new TypeError("First argument to race must be an array of functions"));if(!n.length)return t();for(var r=0,e=n.length;r<e;r++)y(n[r])(t)}function gr(n,r,e,u){bt(t(n).reverse(),r,e,u)}function br(n){var r=y(n);return e(function(n,e){return n.push(function(n,r){var u;n?e(null,{error:n}):(u=arguments.length<=2?r:t(arguments,1),e(null,{value:u}))}),r.apply(this,n)})}function jr(n){var t;return J(n)?t=Cn(n,br):(t={},Pn(n,function(n,r){t[r]=br.call(this,n)})),t}function Sr(n,t,r,e){Xt(n,t,function(n,t){r(n,function(n,r){t(n,!r)})},e)}var kr=En(Sr),Lr=Fn(Sr),Or=Ln(Lr,1);function wr(n){return function(){return n}}function xr(n,t,r){var e=5,u=0,i={times:e,intervalFunc:wr(u)};if(arguments.length<3&&"function"==typeof n?(r=t||P,t=n):(!function(n,t){if("object"==typeof t)n.times=+t.times||e,n.intervalFunc="function"==typeof t.interval?t.interval:wr(+t.interval||u),n.errorFilter=t.errorFilter;else{if("number"!=typeof t&&"string"!=typeof t)throw new Error("Invalid arguments for async.retry");n.times=+t||e}}(i,n),r=r||P),"function"!=typeof t)throw new Error("Invalid arguments for async.retry");var o=y(t),c=1;!function n(){o(function(t){t&&c++<i.times&&("function"!=typeof i.errorFilter||i.errorFilter(t))?setTimeout(n,i.intervalFunc(c)):r.apply(null,arguments)})}()}var Er=function(n,t){t||(t=n,n=null);var r=y(t);return e(function(t,e){function u(n){r.apply(null,t.concat(n))}n?xr(n,u,e):xr(u,e)})};function Ar(n,t){pr(gt,n,t)}var Tr=En(At(Boolean,Et)),Br=Fn(At(Boolean,Et)),Fr=Ln(Br,1);function Ir(n,t,r){var e=y(t);function u(n,t){var r=n.criteria,e=t.criteria;return r<e?-1:r>e?1:0}Tn(n,function(n,t){e(n,function(r,e){if(r)return t(r);t(null,{value:n,criteria:e})})},function(n,t){if(n)return r(n);r(null,Cn(t.sort(u),Ht("value")))})}function _r(n,t,r){var u=y(n);return e(function(e,i){var o,c=!1;e.push(function(){c||(i.apply(null,arguments),clearTimeout(o))}),o=setTimeout(function(){var t=n.name||"anonymous",e=new Error('Callback function "'+t+'" timed out.');e.code="ETIMEDOUT",r&&(e.info=r),c=!0,i(e)},t),u.apply(null,e)})}var Mr=Math.ceil,Ur=Math.max;function qr(n,t,r,e){var u=y(r);In(function(n,t,r,e){for(var u=-1,i=Ur(Mr((t-n)/(r||1)),0),o=Array(i);i--;)o[e?i:++u]=n,n+=r;return o}(0,n,1),t,u,e)}var zr=Ln(qr,1/0),Pr=Ln(qr,1);function Vr(n,t,r,e){arguments.length<=3&&(e=r,r=t,t=J(n)?[]:{}),e=V(e||P);var u=y(r);xn(n,function(n,r,e){u(t,n,r,e)},function(n){e(n,t)})}function Dr(n,r){var e,u=null;r=r||P,Ct(n,function(n,r){y(n)(function(n,i){e=arguments.length>2?t(arguments,1):i,u=n,r(!n)})},function(){r(u,e)})}function Rr(n){return function(){return(n.unmemoized||n).apply(null,arguments)}}function Cr(n,r,e){e=jn(e||P);var u=y(r);if(!n())return e(null);var i=function(r){if(r)return e(r);if(n())return u(i);var o=t(arguments,1);e.apply(null,[null].concat(o))};u(i)}function $r(n,t,r){Cr(function(){return!n.apply(this,arguments)},t,r)}var Wr=function(n,r){if(r=V(r||P),!J(n))return r(new Error("First argument to waterfall must be an array of functions"));if(!n.length)return r();var e=0;function u(t){var r=y(n[e++]);t.push(jn(i)),r.apply(null,t)}function i(i){if(i||e===n.length)return r.apply(null,arguments);u(t(arguments,1))}u([])},Nr={apply:r,applyEach:Bn,applyEachSeries:Mn,asyncify:l,auto:Rn,autoInject:vt,cargo:mt,compose:St,concat:Ot,concatLimit:Lt,concatSeries:wt,constant:xt,detect:Bt,detectLimit:Ft,detectSeries:It,dir:Mt,doDuring:Ut,doUntil:zt,doWhilst:qt,during:Pt,each:Dt,eachLimit:Rt,eachOf:xn,eachOfLimit:kn,eachOfSeries:gt,eachSeries:Ct,ensureAsync:$t,every:Nt,everyLimit:Qt,everySeries:Gt,filter:Yt,filterLimit:Zt,filterSeries:nr,forever:tr,groupBy:er,groupByLimit:rr,groupBySeries:ur,log:ir,map:Tn,mapLimit:In,mapSeries:_n,mapValues:cr,mapValuesLimit:or,mapValuesSeries:ar,memoize:lr,nextTick:sr,parallel:vr,parallelLimit:hr,priorityQueue:dr,queue:yr,race:mr,reduce:bt,reduceRight:gr,reflect:br,reflectAll:jr,reject:kr,rejectLimit:Lr,rejectSeries:Or,retry:xr,retryable:Er,seq:jt,series:Ar,setImmediate:f,some:Tr,someLimit:Br,someSeries:Fr,sortBy:Ir,timeout:_r,times:zr,timesLimit:qr,timesSeries:Pr,transform:Vr,tryEach:Dr,unmemoize:Rr,until:$r,waterfall:Wr,whilst:Cr,all:Nt,allLimit:Qt,allSeries:Gt,any:Tr,anyLimit:Br,anySeries:Fr,find:Bt,findLimit:Ft,findSeries:It,forEach:Dt,forEachSeries:Ct,forEachLimit:Rt,forEachOf:xn,forEachOfSeries:gt,forEachOfLimit:kn,inject:bt,foldl:bt,foldr:gr,select:Yt,selectLimit:Zt,selectSeries:nr,wrapSync:l};n.default=Nr,n.apply=r,n.applyEach=Bn,n.applyEachSeries=Mn,n.asyncify=l,n.auto=Rn,n.autoInject=vt,n.cargo=mt,n.compose=St,n.concat=Ot,n.concatLimit=Lt,n.concatSeries=wt,n.constant=xt,n.detect=Bt,n.detectLimit=Ft,n.detectSeries=It,n.dir=Mt,n.doDuring=Ut,n.doUntil=zt,n.doWhilst=qt,n.during=Pt,n.each=Dt,n.eachLimit=Rt,n.eachOf=xn,n.eachOfLimit=kn,n.eachOfSeries=gt,n.eachSeries=Ct,n.ensureAsync=$t,n.every=Nt,n.everyLimit=Qt,n.everySeries=Gt,n.filter=Yt,n.filterLimit=Zt,n.filterSeries=nr,n.forever=tr,n.groupBy=er,n.groupByLimit=rr,n.groupBySeries=ur,n.log=ir,n.map=Tn,n.mapLimit=In,n.mapSeries=_n,n.mapValues=cr,n.mapValuesLimit=or,n.mapValuesSeries=ar,n.memoize=lr,n.nextTick=sr,n.parallel=vr,n.parallelLimit=hr,n.priorityQueue=dr,n.queue=yr,n.race=mr,n.reduce=bt,n.reduceRight=gr,n.reflect=br,n.reflectAll=jr,n.reject=kr,n.rejectLimit=Lr,n.rejectSeries=Or,n.retry=xr,n.retryable=Er,n.seq=jt,n.series=Ar,n.setImmediate=f,n.some=Tr,n.someLimit=Br,n.someSeries=Fr,n.sortBy=Ir,n.timeout=_r,n.times=zr,n.timesLimit=qr,n.timesSeries=Pr,n.transform=Vr,n.tryEach=Dr,n.unmemoize=Rr,n.until=$r,n.waterfall=Wr,n.whilst=Cr,n.all=Nt,n.allLimit=Qt,n.allSeries=Gt,n.any=Tr,n.anyLimit=Br,n.anySeries=Fr,n.find=Bt,n.findLimit=Ft,n.findSeries=It,n.forEach=Dt,n.forEachSeries=Ct,n.forEachLimit=Rt,n.forEachOf=xn,n.forEachOfSeries=gt,n.forEachOfLimit=kn,n.inject=bt,n.foldl=bt,n.foldr=gr,n.select=Yt,n.selectLimit=Zt,n.selectSeries=nr,n.wrapSync=l,Object.defineProperty(n,"__esModule",{value:!0})});

}).call(this,require('_process'),typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {},require("timers").setImmediate)
},{"_process":26,"timers":27}],2:[function(require,module,exports){
!function(){"use strict";var r=function(r){null==r&&(r=1024);var e=function(r,e,t){for(var n=e;n<t;n++)r[n]=1-n;if(0<u.array[u.array.length-1]){for(var a=u.array.length-2;0<u.array[a];)a--;r[e]=-a}},t=function(r,e,t){for(var n=e;n<t;n++)r[n]=-n-1},a=function(r){var a=2*r,i=n(s.signed,s.bytes,a);e(i,s.array.length,a),i.set(s.array),s.array=null,s.array=i;var o=n(u.signed,u.bytes,a);t(o,u.array.length,a),o.set(u.array),u.array=null,u.array=o},i=1,s={signed:!0,bytes:4,array:n(!0,4,r)},u={signed:!0,bytes:4,array:n(!0,4,r)};return s.array[0]=1,u.array[0]=0,e(s.array,1,s.array.length),t(u.array,1,u.array.length),{getBaseBuffer:function(){return s.array},getCheckBuffer:function(){return u.array},loadBaseBuffer:function(r){return s.array=r,this},loadCheckBuffer:function(r){return u.array=r,this},size:function(){return Math.max(s.array.length,u.array.length)},getBase:function(r){return s.array.length-1<r?1-r:s.array[r]},getCheck:function(r){return u.array.length-1<r?-r-1:u.array[r]},setBase:function(r,e){s.array.length-1<r&&a(r),s.array[r]=e},setCheck:function(r,e){u.array.length-1<r&&a(r),u.array[r]=e},setFirstUnusedNode:function(r){i=r},getFirstUnusedNode:function(){return i},shrink:function(){for(var r=this.size()-1;!(0<=u.array[r]);)r--;s.array=s.array.subarray(0,r+2),u.array=u.array.subarray(0,r+2)},calc:function(){for(var r=0,e=u.array.length,t=0;t<e;t++)u.array[t]<0&&r++;return{all:e,unused:r,efficiency:(e-r)/e}},dump:function(){var r,e="",t="";for(r=0;r<s.array.length;r++)e=e+" "+this.getBase(r);for(r=0;r<u.array.length;r++)t=t+" "+this.getCheck(r);return console.log("base:"+e),console.log("chck:"+t),"base:"+e+" chck:"+t}}};function e(e){this.bc=r(e),this.keys=[]}function t(r){this.bc=r,this.bc.shrink()}e.prototype.append=function(r,e){return this.keys.push({k:r,v:e}),this},e.prototype.build=function(r,e){if(null==r&&(r=this.keys),null==r)return new t(this.bc);null==e&&(e=!1);var n=r.map(function(r){return{k:i(r.k+"\0"),v:r.v}});return this.keys=e?n:n.sort(function(r,e){for(var t=r.k,n=e.k,a=Math.min(t.length,n.length),i=0;i<a;i++)if(t[i]!==n[i])return t[i]-n[i];return t.length-n.length}),n=null,this._build(0,0,0,this.keys.length),new t(this.bc)},e.prototype._build=function(r,e,t,n){var a=this.getChildrenInfo(e,t,n),i=this.findAllocatableBase(a);this.setBC(r,a,i);for(var s=0;s<a.length;s+=3){var u=a[s];if(0!==u){var o=a[s+1],f=a[s+2],c=i+u;this._build(c,e+1,o,f)}}},e.prototype.getChildrenInfo=function(r,e,t){var n=this.keys[e].k[r],a=0,i=new Int32Array(3*t);i[a++]=n,i[a++]=e;for(var s=e,u=e;s<e+t;s++){var o=this.keys[s].k[r];n!==o&&(i[a++]=s-u,i[a++]=o,i[a++]=s,n=o,u=s)}return i[a++]=s-u,i=i.subarray(0,a)},e.prototype.setBC=function(r,e,t){var n,a=this.bc;for(a.setBase(r,t),n=0;n<e.length;n+=3){var i=e[n],s=t+i,u=-a.getBase(s),o=-a.getCheck(s);s!==a.getFirstUnusedNode()?a.setCheck(u,-o):a.setFirstUnusedNode(o),a.setBase(o,-u);var f=r;if(a.setCheck(s,f),0===i){var c=e[n+1],h=this.keys[c].v;null==h&&(h=0);var y=-h-1;a.setBase(s,y)}}},e.prototype.findAllocatableBase=function(r){for(var e,t=this.bc,n=t.getFirstUnusedNode();;)if((e=n-r[0])<0)n=-t.getCheck(n);else{for(var a=!0,i=0;i<r.length;i+=3){var s=e+r[i];if(!this.isUnusedNode(s)){n=-t.getCheck(n),a=!1;break}}if(a)return e}},e.prototype.isUnusedNode=function(r){var e=this.bc.getCheck(r);return 0!==r&&e<0},t.prototype.contain=function(r){for(var e=this.bc,t=i(r+="\0"),n=0,a=-1,s=0;s<t.length;s++){var u=t[s];if(-1===(a=this.traverse(n,u)))return!1;if(e.getBase(a)<=0)return!0;n=a}return!1},t.prototype.lookup=function(r){for(var e=i(r+="\0"),t=0,n=-1,a=0;a<e.length;a++){var s=e[a];if(-1===(n=this.traverse(t,s)))return-1;t=n}var u=this.bc.getBase(n);return u<=0?-u-1:-1},t.prototype.commonPrefixSearch=function(r){for(var e=i(r),t=0,n=-1,u=[],o=0;o<e.length;o++){var f=e[o];if(-1===(n=this.traverse(t,f)))break;t=n;var c=this.traverse(n,0);if(-1!==c){var h=this.bc.getBase(c),y={};h<=0&&(y.v=-h-1),y.k=s(a(e,0,o+1)),u.push(y)}}return u},t.prototype.traverse=function(r,e){var t=this.bc.getBase(r)+e;return this.bc.getCheck(t)===r?t:-1},t.prototype.size=function(){return this.bc.size()},t.prototype.calc=function(){return this.bc.calc()},t.prototype.dump=function(){return this.bc.dump()};var n=function(r,e,t){if(r)switch(e){case 1:return new Int8Array(t);case 2:return new Int16Array(t);case 4:return new Int32Array(t);default:throw new RangeError("Invalid newArray parameter element_bytes:"+e)}else switch(e){case 1:return new Uint8Array(t);case 2:return new Uint16Array(t);case 4:return new Uint32Array(t);default:throw new RangeError("Invalid newArray parameter element_bytes:"+e)}},a=function(r,e,t){var n=new ArrayBuffer(t),a=new Uint8Array(n,0,t),i=r.subarray(e,t);return a.set(i),a},i=function(r){for(var e=new Uint8Array(new ArrayBuffer(4*r.length)),t=0,n=0;t<r.length;){var a,i=r.charCodeAt(t++);if(i>=55296&&i<=56319){var s=i,u=r.charCodeAt(t++);if(!(u>=56320&&u<=57343))return null;a=1024*(s-55296)+65536+(u-56320)}else a=i;a<128?e[n++]=a:a<2048?(e[n++]=a>>>6|192,e[n++]=63&a|128):a<65536?(e[n++]=a>>>12|224,e[n++]=a>>6&63|128,e[n++]=63&a|128):a<1<<21&&(e[n++]=a>>>18|240,e[n++]=a>>12&63|128,e[n++]=a>>6&63|128,e[n++]=63&a|128)}return e.subarray(0,n)},s=function(r){for(var e,t,n,a,i="",s=0;s<r.length;)(e=(t=r[s++])<128?t:t>>5==6?(31&t)<<6|63&r[s++]:t>>4==14?(15&t)<<12|(63&r[s++])<<6|63&r[s++]:(7&t)<<18|(63&r[s++])<<12|(63&r[s++])<<6|63&r[s++])<65536?i+=String.fromCharCode(e):(n=55296|(e-=65536)>>10,a=56320|1023&e,i+=String.fromCharCode(n,a));return i},u={builder:function(r){return new e(r)},load:function(e,n){var a=r(0);return a.loadBaseBuffer(e),a.loadCheckBuffer(n),new t(a)}};"undefined"==typeof module?window.doublearray=u:module.exports=u}();

},{}],3:[function(require,module,exports){
"use strict";var ViterbiBuilder=require("./viterbi/ViterbiBuilder"),ViterbiSearcher=require("./viterbi/ViterbiSearcher"),IpadicFormatter=require("./util/IpadicFormatter"),PUNCTUATION=/、|。/;function Tokenizer(t){this.token_info_dictionary=t.token_info_dictionary,this.unknown_dictionary=t.unknown_dictionary,this.viterbi_builder=new ViterbiBuilder(t),this.viterbi_searcher=new ViterbiSearcher(t.connection_costs),this.formatter=new IpadicFormatter}Tokenizer.splitByPunctuation=function(t){for(var e=[],r=t;""!==r;){var i=r.search(PUNCTUATION);if(i<0){e.push(r);break}e.push(r.substring(0,i+1)),r=r.substring(i+1)}return e},Tokenizer.prototype.tokenize=function(t){for(var e=Tokenizer.splitByPunctuation(t),r=[],i=0;i<e.length;i++){var n=e[i];this.tokenizeForSentence(n,r)}return r},Tokenizer.prototype.tokenizeForSentence=function(t,e){null==e&&(e=[]);var r=this.getLattice(t),i=this.viterbi_searcher.search(r),n=0;e.length>0&&(n=e[e.length-1].word_position);for(var o=0;o<i.length;o++){var a,s,u,c=i[o];"KNOWN"===c.type?(s=null==(u=this.token_info_dictionary.getFeatures(c.name))?[]:u.split(","),a=this.formatter.formatEntry(c.name,n+c.start_pos,c.type,s)):"UNKNOWN"===c.type?(s=null==(u=this.unknown_dictionary.getFeatures(c.name))?[]:u.split(","),a=this.formatter.formatUnknownEntry(c.name,n+c.start_pos,c.type,s,c.surface_form)):a=this.formatter.formatEntry(c.name,n+c.start_pos,c.type,[]),e.push(a)}return e},Tokenizer.prototype.getLattice=function(t){return this.viterbi_builder.build(t)},module.exports=Tokenizer;

},{"./util/IpadicFormatter":19,"./viterbi/ViterbiBuilder":21,"./viterbi/ViterbiSearcher":24}],4:[function(require,module,exports){
"use strict";var Tokenizer=require("./Tokenizer"),DictionaryLoader=require("./loader/NodeDictionaryLoader");function TokenizerBuilder(i){null==i.dicPath?this.dic_path="dict/":this.dic_path=i.dicPath}TokenizerBuilder.prototype.build=function(i){new DictionaryLoader(this.dic_path).load(function(e,r){i(e,new Tokenizer(r))})},module.exports=TokenizerBuilder;

},{"./Tokenizer":3,"./loader/NodeDictionaryLoader":16}],5:[function(require,module,exports){
"use strict";function CharacterClass(s,a,i,t,e){this.class_id=s,this.class_name=a,this.is_always_invoke=i,this.is_grouping=t,this.max_length=e}module.exports=CharacterClass;

},{}],6:[function(require,module,exports){
"use strict";var InvokeDefinitionMap=require("./InvokeDefinitionMap"),CharacterClass=require("./CharacterClass"),SurrogateAwareString=require("../util/SurrogateAwareString"),DEFAULT_CATEGORY="DEFAULT";function CharacterDefinition(){this.character_category_map=new Uint8Array(65536),this.compatible_category_map=new Uint32Array(65536),this.invoke_definition_map=null}CharacterDefinition.load=function(r,e,a){var i=new CharacterDefinition;return i.character_category_map=r,i.compatible_category_map=e,i.invoke_definition_map=InvokeDefinitionMap.load(a),i},CharacterDefinition.parseCharCategory=function(r,e){var a=e[1],i=parseInt(e[2]),t=parseInt(e[3]),n=parseInt(e[4]);return!isFinite(i)||0!==i&&1!==i?(console.log("char.def parse error. INVOKE is 0 or 1 in:"+i),null):!isFinite(t)||0!==t&&1!==t?(console.log("char.def parse error. GROUP is 0 or 1 in:"+t),null):!isFinite(n)||n<0?(console.log("char.def parse error. LENGTH is 1 to n:"+n),null):new CharacterClass(r,a,1===i,1===t,n)},CharacterDefinition.parseCategoryMapping=function(r){var e=parseInt(r[1]),a=r[2],i=3<r.length?r.slice(3):[];return(!isFinite(e)||e<0||e>65535)&&console.log("char.def parse error. CODE is invalid:"+e),{start:e,default:a,compatible:i}},CharacterDefinition.parseRangeCategoryMapping=function(r){var e=parseInt(r[1]),a=parseInt(r[2]),i=r[3],t=4<r.length?r.slice(4):[];return(!isFinite(e)||e<0||e>65535)&&console.log("char.def parse error. CODE is invalid:"+e),(!isFinite(a)||a<0||a>65535)&&console.log("char.def parse error. CODE is invalid:"+a),{start:e,end:a,default:i,compatible:t}},CharacterDefinition.prototype.initCategoryMappings=function(r){var e;if(null!=r)for(var a=0;a<r.length;a++){var i=r[a],t=i.end||i.start;for(e=i.start;e<=t;e++){this.character_category_map[e]=this.invoke_definition_map.lookup(i.default);for(var n=0;n<i.compatible.length;n++){var o=this.compatible_category_map[e],c=i.compatible[n];if(null!=c){var l=this.invoke_definition_map.lookup(c);if(null!=l)o|=1<<l,this.compatible_category_map[e]=o}}}}var s=this.invoke_definition_map.lookup(DEFAULT_CATEGORY);if(null!=s)for(e=0;e<this.character_category_map.length;e++)0===this.character_category_map[e]&&(this.character_category_map[e]=1<<s)},CharacterDefinition.prototype.lookupCompatibleCategory=function(r){var e,a=[],i=r.charCodeAt(0);if(i<this.compatible_category_map.length&&(e=this.compatible_category_map[i]),null==e||0===e)return a;for(var t=0;t<32;t++)if(e<<31-t>>>31==1){var n=this.invoke_definition_map.getCharacterClass(t);if(null==n)continue;a.push(n)}return a},CharacterDefinition.prototype.lookup=function(r){var e,a=r.charCodeAt(0);return SurrogateAwareString.isSurrogatePair(r)?e=this.invoke_definition_map.lookup(DEFAULT_CATEGORY):a<this.character_category_map.length&&(e=this.character_category_map[a]),null==e&&(e=this.invoke_definition_map.lookup(DEFAULT_CATEGORY)),this.invoke_definition_map.getCharacterClass(e)},module.exports=CharacterDefinition;

},{"../util/SurrogateAwareString":20,"./CharacterClass":5,"./InvokeDefinitionMap":9}],7:[function(require,module,exports){
"use strict";function ConnectionCosts(o,t){this.forward_dimension=o,this.backward_dimension=t,this.buffer=new Int16Array(o*t+2),this.buffer[0]=o,this.buffer[1]=t}ConnectionCosts.prototype.put=function(o,t,n){var i=o*this.backward_dimension+t+2;if(this.buffer.length<i+1)throw"ConnectionCosts buffer overflow";this.buffer[i]=n},ConnectionCosts.prototype.get=function(o,t){var n=o*this.backward_dimension+t+2;if(this.buffer.length<n+1)throw"ConnectionCosts buffer overflow";return this.buffer[n]},ConnectionCosts.prototype.loadConnectionCosts=function(o){this.forward_dimension=o[0],this.backward_dimension=o[1],this.buffer=o},module.exports=ConnectionCosts;

},{}],8:[function(require,module,exports){
"use strict";var doublearray=require("doublearray"),TokenInfoDictionary=require("./TokenInfoDictionary"),ConnectionCosts=require("./ConnectionCosts"),UnknownDictionary=require("./UnknownDictionary");function DynamicDictionaries(n,i,o,t){this.trie=null!=n?n:doublearray.builder(0).build([{k:"",v:1}]),this.token_info_dictionary=null!=i?i:new TokenInfoDictionary,this.connection_costs=null!=o?o:new ConnectionCosts(0,0),this.unknown_dictionary=null!=t?t:new UnknownDictionary}DynamicDictionaries.prototype.loadTrie=function(n,i){return this.trie=doublearray.load(n,i),this},DynamicDictionaries.prototype.loadTokenInfoDictionaries=function(n,i,o){return this.token_info_dictionary.loadDictionary(n),this.token_info_dictionary.loadPosVector(i),this.token_info_dictionary.loadTargetMap(o),this},DynamicDictionaries.prototype.loadConnectionCosts=function(n){return this.connection_costs.loadConnectionCosts(n),this},DynamicDictionaries.prototype.loadUnknownDictionaries=function(n,i,o,t,r,e){return this.unknown_dictionary.loadUnknownDictionaries(n,i,o,t,r,e),this},module.exports=DynamicDictionaries;

},{"./ConnectionCosts":7,"./TokenInfoDictionary":10,"./UnknownDictionary":11,"doublearray":2}],9:[function(require,module,exports){
"use strict";var ByteBuffer=require("../util/ByteBuffer"),CharacterClass=require("./CharacterClass");function InvokeDefinitionMap(){this.map=[],this.lookup_table={}}InvokeDefinitionMap.load=function(t){for(var e=new InvokeDefinitionMap,n=[],i=new ByteBuffer(t);i.position+1<i.size();){var o=n.length,r=i.get(),a=i.get(),u=i.getInt(),p=i.getString();n.push(new CharacterClass(o,p,r,a,u))}return e.init(n),e},InvokeDefinitionMap.prototype.init=function(t){if(null!=t)for(var e=0;e<t.length;e++){var n=t[e];this.map[e]=n,this.lookup_table[n.class_name]=e}},InvokeDefinitionMap.prototype.getCharacterClass=function(t){return this.map[t]},InvokeDefinitionMap.prototype.lookup=function(t){var e=this.lookup_table[t];return null==e?null:e},InvokeDefinitionMap.prototype.toBuffer=function(){for(var t=new ByteBuffer,e=0;e<this.map.length;e++){var n=this.map[e];t.put(n.is_always_invoke),t.put(n.is_grouping),t.putInt(n.max_length),t.putString(n.class_name)}return t.shrink(),t.buffer},module.exports=InvokeDefinitionMap;

},{"../util/ByteBuffer":18,"./CharacterClass":5}],10:[function(require,module,exports){
"use strict";var ByteBuffer=require("../util/ByteBuffer");function TokenInfoDictionary(){this.dictionary=new ByteBuffer(10485760),this.target_map={},this.pos_buffer=new ByteBuffer(10485760)}TokenInfoDictionary.prototype.buildDictionary=function(t){for(var i={},r=0;r<t.length;r++){var n=t[r];if(!(n.length<4)){var o=n[0],e=n[1],a=n[2],f=n[3],s=n.slice(4).join(",");isFinite(e)&&isFinite(a)&&isFinite(f)||console.log(n),i[this.put(e,a,f,o,s)]=o}}return this.dictionary.shrink(),this.pos_buffer.shrink(),i},TokenInfoDictionary.prototype.put=function(t,i,r,n,o){var e=this.dictionary.position,a=this.pos_buffer.position;return this.dictionary.putShort(t),this.dictionary.putShort(i),this.dictionary.putShort(r),this.dictionary.putInt(a),this.pos_buffer.putString(n+","+o),e},TokenInfoDictionary.prototype.addMapping=function(t,i){var r=this.target_map[t];null==r&&(r=[]),r.push(i),this.target_map[t]=r},TokenInfoDictionary.prototype.targetMapToBuffer=function(){var t=new ByteBuffer,i=Object.keys(this.target_map).length;for(var r in t.putInt(i),this.target_map){var n=this.target_map[r],o=n.length;t.putInt(parseInt(r)),t.putInt(o);for(var e=0;e<n.length;e++)t.putInt(n[e])}return t.shrink()},TokenInfoDictionary.prototype.loadDictionary=function(t){return this.dictionary=new ByteBuffer(t),this},TokenInfoDictionary.prototype.loadPosVector=function(t){return this.pos_buffer=new ByteBuffer(t),this},TokenInfoDictionary.prototype.loadTargetMap=function(t){var i=new ByteBuffer(t);for(i.position=0,this.target_map={},i.readInt();!(i.buffer.length<i.position+1);)for(var r=i.readInt(),n=i.readInt(),o=0;o<n;o++){var e=i.readInt();this.addMapping(r,e)}return this},TokenInfoDictionary.prototype.getFeatures=function(t){var i=parseInt(t);if(isNaN(i))return"";var r=this.dictionary.getInt(i+6);return this.pos_buffer.getString(r)},module.exports=TokenInfoDictionary;

},{"../util/ByteBuffer":18}],11:[function(require,module,exports){
"use strict";var TokenInfoDictionary=require("./TokenInfoDictionary"),CharacterDefinition=require("./CharacterDefinition"),ByteBuffer=require("../util/ByteBuffer");function UnknownDictionary(){this.dictionary=new ByteBuffer(10485760),this.target_map={},this.pos_buffer=new ByteBuffer(10485760),this.character_definition=null}UnknownDictionary.prototype=Object.create(TokenInfoDictionary.prototype),UnknownDictionary.prototype.characterDefinition=function(t){return this.character_definition=t,this},UnknownDictionary.prototype.lookup=function(t){return this.character_definition.lookup(t)},UnknownDictionary.prototype.lookupCompatibleCategory=function(t){return this.character_definition.lookupCompatibleCategory(t)},UnknownDictionary.prototype.loadUnknownDictionaries=function(t,i,n,o,r,e){this.loadDictionary(t),this.loadPosVector(i),this.loadTargetMap(n),this.character_definition=CharacterDefinition.load(o,r,e)},module.exports=UnknownDictionary;

},{"../util/ByteBuffer":18,"./CharacterDefinition":6,"./TokenInfoDictionary":10}],12:[function(require,module,exports){
"use strict";var CharacterDefinition=require("../CharacterDefinition"),InvokeDefinitionMap=require("../InvokeDefinitionMap"),CATEGORY_DEF_PATTERN=/^(\w+)\s+(\d)\s+(\d)\s+(\d)/,CATEGORY_MAPPING_PATTERN=/^(0x[0-9A-F]{4})(?:\s+([^#\s]+))(?:\s+([^#\s]+))*/,RANGE_CATEGORY_MAPPING_PATTERN=/^(0x[0-9A-F]{4})\.\.(0x[0-9A-F]{4})(?:\s+([^#\s]+))(?:\s+([^#\s]+))*/;function CharacterDefinitionBuilder(){this.char_def=new CharacterDefinition,this.char_def.invoke_definition_map=new InvokeDefinitionMap,this.character_category_definition=[],this.category_mapping=[]}CharacterDefinitionBuilder.prototype.putLine=function(i){var e=CATEGORY_DEF_PATTERN.exec(i);if(null==e){var t=CATEGORY_MAPPING_PATTERN.exec(i);if(null!=t){var r=CharacterDefinition.parseCategoryMapping(t);this.category_mapping.push(r)}var a=RANGE_CATEGORY_MAPPING_PATTERN.exec(i);if(null!=a){var n=CharacterDefinition.parseRangeCategoryMapping(a);this.category_mapping.push(n)}}else{var o=this.character_category_definition.length,c=CharacterDefinition.parseCharCategory(o,e);if(null==c)return;this.character_category_definition.push(c)}},CharacterDefinitionBuilder.prototype.build=function(){return this.char_def.invoke_definition_map.init(this.character_category_definition),this.char_def.initCategoryMappings(this.category_mapping),this.char_def},module.exports=CharacterDefinitionBuilder;

},{"../CharacterDefinition":6,"../InvokeDefinitionMap":9}],13:[function(require,module,exports){
"use strict";var ConnectionCosts=require("../ConnectionCosts");function ConnectionCostsBuilder(){this.lines=0,this.connection_cost=null}ConnectionCostsBuilder.prototype.putLine=function(t){if(0===this.lines){var n=t.split(" "),i=n[0],o=n[1];if(i<0||o<0)throw"Parse error of matrix.def";return this.connection_cost=new ConnectionCosts(i,o),this.lines++,this}var s=t.split(" ");if(3!==s.length)return this;var e=parseInt(s[0]),r=parseInt(s[1]),c=parseInt(s[2]);if(e<0||r<0||!isFinite(e)||!isFinite(r)||this.connection_cost.forward_dimension<=e||this.connection_cost.backward_dimension<=r)throw"Parse error of matrix.def";return this.connection_cost.put(e,r,c),this.lines++,this},ConnectionCostsBuilder.prototype.build=function(){return this.connection_cost},module.exports=ConnectionCostsBuilder;

},{"../ConnectionCosts":7}],14:[function(require,module,exports){
"use strict";var doublearray=require("doublearray"),DynamicDictionaries=require("../DynamicDictionaries"),TokenInfoDictionary=require("../TokenInfoDictionary"),ConnectionCostsBuilder=require("./ConnectionCostsBuilder"),CharacterDefinitionBuilder=require("./CharacterDefinitionBuilder"),UnknownDictionary=require("../UnknownDictionary");function DictionaryBuilder(){this.tid_entries=[],this.unk_entries=[],this.cc_builder=new ConnectionCostsBuilder,this.cd_builder=new CharacterDefinitionBuilder}DictionaryBuilder.prototype.addTokenInfoDictionary=function(i){var n=i.split(",");return this.tid_entries.push(n),this},DictionaryBuilder.prototype.putCostMatrixLine=function(i){return this.cc_builder.putLine(i),this},DictionaryBuilder.prototype.putCharDefLine=function(i){return this.cd_builder.putLine(i),this},DictionaryBuilder.prototype.putUnkDefLine=function(i){return this.unk_entries.push(i.split(",")),this},DictionaryBuilder.prototype.build=function(){var i=this.buildTokenInfoDictionary(),n=this.buildUnknownDictionary();return new DynamicDictionaries(i.trie,i.token_info_dictionary,this.cc_builder.build(),n)},DictionaryBuilder.prototype.buildTokenInfoDictionary=function(){var i=new TokenInfoDictionary,n=i.buildDictionary(this.tid_entries),r=this.buildDoubleArray();for(var t in n){var e=n[t],o=r.lookup(e);i.addMapping(o,t)}return{trie:r,token_info_dictionary:i}},DictionaryBuilder.prototype.buildUnknownDictionary=function(){var i=new UnknownDictionary,n=i.buildDictionary(this.unk_entries),r=this.cd_builder.build();for(var t in i.characterDefinition(r),n){var e=n[t],o=r.invoke_definition_map.lookup(e);i.addMapping(o,t)}return i},DictionaryBuilder.prototype.buildDoubleArray=function(){var i=0,n=this.tid_entries.map(function(n){return{k:n[0],v:i++}});return doublearray.builder(1048576).build(n)},module.exports=DictionaryBuilder;

},{"../DynamicDictionaries":8,"../TokenInfoDictionary":10,"../UnknownDictionary":11,"./CharacterDefinitionBuilder":12,"./ConnectionCostsBuilder":13,"doublearray":2}],15:[function(require,module,exports){
"use strict";var TokenizerBuilder=require("./TokenizerBuilder"),DictionaryBuilder=require("./dict/builder/DictionaryBuilder"),kuromoji={builder:function(r){return new TokenizerBuilder(r)},dictionaryBuilder:function(){return new DictionaryBuilder}};module.exports=kuromoji;

},{"./TokenizerBuilder":4,"./dict/builder/DictionaryBuilder":14}],16:[function(require,module,exports){
"use strict";var zlib=require("zlibjs/bin/gunzip.min.js"),DictionaryLoader=require("./DictionaryLoader");function BrowserDictionaryLoader(r){DictionaryLoader.apply(this,[r])}BrowserDictionaryLoader.prototype=Object.create(DictionaryLoader.prototype),BrowserDictionaryLoader.prototype.loadArrayBuffer=function(r,e){var o=new XMLHttpRequest;o.open("GET",r,!0),o.responseType="arraybuffer",o.onload=function(){if(this.status>0&&200!==this.status)e(o.statusText,null);else{var r=this.response;e(null,r)}},o.onerror=function(r){e(r,null)},o.send()},module.exports=BrowserDictionaryLoader;

},{"./DictionaryLoader":17,"zlibjs/bin/gunzip.min.js":28}],17:[function(require,module,exports){
"use strict";var path=require("path"),async=require("async"),DynamicDictionaries=require("../dict/DynamicDictionaries");function DictionaryLoader(n){this.dic=new DynamicDictionaries,this.dic_path=n}DictionaryLoader.prototype.loadArrayBuffer=function(n,t){throw new Error("DictionaryLoader#loadArrayBuffer should be overwrite")},DictionaryLoader.prototype.load=function(n){var t=this.dic,i=this.dic_path,r=this.loadArrayBuffer;async.parallel([function(n){async.map(["base.dat.gz","check.dat.gz"],function(n,t){r(path.join(i,n),function(n,i){if(n)return t(n);t(null,i)})},function(i,r){if(i)return n(i);var a=new Int32Array(r[0]),o=new Int32Array(r[1]);t.loadTrie(a,o),n(null)})},function(n){async.map(["tid.dat.gz","tid_pos.dat.gz","tid_map.dat.gz"],function(n,t){r(path.join(i,n),function(n,i){if(n)return t(n);t(null,i)})},function(i,r){if(i)return n(i);var a=new Uint8Array(r[0]),o=new Uint8Array(r[1]),e=new Uint8Array(r[2]);t.loadTokenInfoDictionaries(a,o,e),n(null)})},function(n){r(path.join(i,"cc.dat.gz"),function(i,r){if(i)return n(i);var a=new Int16Array(r);t.loadConnectionCosts(a),n(null)})},function(n){async.map(["unk.dat.gz","unk_pos.dat.gz","unk_map.dat.gz","unk_char.dat.gz","unk_compat.dat.gz","unk_invoke.dat.gz"],function(n,t){r(path.join(i,n),function(n,i){if(n)return t(n);t(null,i)})},function(i,r){if(i)return n(i);var a=new Uint8Array(r[0]),o=new Uint8Array(r[1]),e=new Uint8Array(r[2]),c=new Uint8Array(r[3]),u=new Uint32Array(r[4]),d=new Uint8Array(r[5]);t.loadUnknownDictionaries(a,o,e,c,u,d),n(null)})}],function(i){n(i,t)})},module.exports=DictionaryLoader;

},{"../dict/DynamicDictionaries":8,"async":1,"path":25}],18:[function(require,module,exports){
"use strict";var stringToUtf8Bytes=function(t){for(var r=new Uint8Array(4*t.length),e=0,i=0;e<t.length;){var f,o=t.charCodeAt(e++);if(o>=55296&&o<=56319){var n=o,u=t.charCodeAt(e++);if(!(u>=56320&&u<=57343))return null;f=1024*(n-55296)+65536+(u-56320)}else f=o;f<128?r[i++]=f:f<2048?(r[i++]=f>>>6|192,r[i++]=63&f|128):f<65536?(r[i++]=f>>>12|224,r[i++]=f>>6&63|128,r[i++]=63&f|128):f<1<<21&&(r[i++]=f>>>18|240,r[i++]=f>>12&63|128,r[i++]=f>>6&63|128,r[i++]=63&f|128)}return r.subarray(0,i)},utf8BytesToString=function(t){for(var r,e,i,f,o="",n=0;n<t.length;)(r=(e=t[n++])<128?e:e>>5==6?(31&e)<<6|63&t[n++]:e>>4==14?(15&e)<<12|(63&t[n++])<<6|63&t[n++]:(7&e)<<18|(63&t[n++])<<12|(63&t[n++])<<6|63&t[n++])<65536?o+=String.fromCharCode(r):(i=55296|(r-=65536)>>10,f=56320|1023&r,o+=String.fromCharCode(i,f));return o};function ByteBuffer(t){var r;if(null==t)r=1048576;else{if("number"!=typeof t){if(t instanceof Uint8Array)return this.buffer=t,void(this.position=0);throw typeof t+" is invalid parameter type for ByteBuffer constructor"}r=t}this.buffer=new Uint8Array(r),this.position=0}ByteBuffer.prototype.size=function(){return this.buffer.length},ByteBuffer.prototype.reallocate=function(){var t=new Uint8Array(2*this.buffer.length);t.set(this.buffer),this.buffer=t},ByteBuffer.prototype.shrink=function(){return this.buffer=this.buffer.subarray(0,this.position),this.buffer},ByteBuffer.prototype.put=function(t){this.buffer.length<this.position+1&&this.reallocate(),this.buffer[this.position++]=t},ByteBuffer.prototype.get=function(t){return null==t&&(t=this.position,this.position+=1),this.buffer.length<t+1?0:this.buffer[t]},ByteBuffer.prototype.putShort=function(t){if(65535<t)throw t+" is over short value";var r=255&t,e=(65280&t)>>8;this.put(r),this.put(e)},ByteBuffer.prototype.getShort=function(t){if(null==t&&(t=this.position,this.position+=2),this.buffer.length<t+2)return 0;var r=this.buffer[t],e=(this.buffer[t+1]<<8)+r;return 32768&e&&(e=-(e-1^65535)),e},ByteBuffer.prototype.putInt=function(t){if(4294967295<t)throw t+" is over integer value";var r=255&t,e=(65280&t)>>8,i=(16711680&t)>>16,f=(4278190080&t)>>24;this.put(r),this.put(e),this.put(i),this.put(f)},ByteBuffer.prototype.getInt=function(t){if(null==t&&(t=this.position,this.position+=4),this.buffer.length<t+4)return 0;var r=this.buffer[t],e=this.buffer[t+1],i=this.buffer[t+2];return(this.buffer[t+3]<<24)+(i<<16)+(e<<8)+r},ByteBuffer.prototype.readInt=function(){var t=this.position;return this.position+=4,this.getInt(t)},ByteBuffer.prototype.putString=function(t){for(var r=stringToUtf8Bytes(t),e=0;e<r.length;e++)this.put(r[e]);this.put(0)},ByteBuffer.prototype.getString=function(t){var r,e=[];for(null==t&&(t=this.position);!(this.buffer.length<t+1)&&0!==(r=this.get(t++));)e.push(r);return this.position=t,utf8BytesToString(e)},module.exports=ByteBuffer;

},{}],19:[function(require,module,exports){
"use strict";function IpadicFormatter(){}IpadicFormatter.prototype.formatEntry=function(o,t,r,a){var e={};return e.word_id=o,e.word_type=r,e.word_position=t,e.surface_form=a[0],e.pos=a[1],e.pos_detail_1=a[2],e.pos_detail_2=a[3],e.pos_detail_3=a[4],e.conjugated_type=a[5],e.conjugated_form=a[6],e.basic_form=a[7],e.reading=a[8],e.pronunciation=a[9],e},IpadicFormatter.prototype.formatUnknownEntry=function(o,t,r,a,e){var _={};return _.word_id=o,_.word_type=r,_.word_position=t,_.surface_form=e,_.pos=a[1],_.pos_detail_1=a[2],_.pos_detail_2=a[3],_.pos_detail_3=a[4],_.conjugated_type=a[5],_.conjugated_form=a[6],_.basic_form=a[7],_},module.exports=IpadicFormatter;

},{}],20:[function(require,module,exports){
"use strict";function SurrogateAwareString(t){this.str=t,this.index_mapping=[];for(var r=0;r<t.length;r++){var i=t.charAt(r);this.index_mapping.push(r),SurrogateAwareString.isSurrogatePair(i)&&r++}this.length=this.index_mapping.length}SurrogateAwareString.prototype.slice=function(t){if(this.index_mapping.length<=t)return"";var r=this.index_mapping[t];return this.str.slice(r)},SurrogateAwareString.prototype.charAt=function(t){if(this.str.length<=t)return"";var r=this.index_mapping[t],i=this.index_mapping[t+1];return null==i?this.str.slice(r):this.str.slice(r,i)},SurrogateAwareString.prototype.charCodeAt=function(t){if(this.index_mapping.length<=t)return NaN;var r,i=this.index_mapping[t],e=this.str.charCodeAt(i);return e>=55296&&e<=56319&&i<this.str.length&&(r=this.str.charCodeAt(i+1))>=56320&&r<=57343?1024*(e-55296)+r-56320+65536:e},SurrogateAwareString.prototype.toString=function(){return this.str},SurrogateAwareString.isSurrogatePair=function(t){var r=t.charCodeAt(0);return r>=55296&&r<=56319},module.exports=SurrogateAwareString;

},{}],21:[function(require,module,exports){
"use strict";var ViterbiNode=require("./ViterbiNode"),ViterbiLattice=require("./ViterbiLattice"),SurrogateAwareString=require("../util/SurrogateAwareString");function ViterbiBuilder(i){this.trie=i.trie,this.token_info_dictionary=i.token_info_dictionary,this.unknown_dictionary=i.unknown_dictionary}ViterbiBuilder.prototype.build=function(i){for(var t,r,n,e,o,a=new ViterbiLattice,c=new SurrogateAwareString(i),d=0;d<c.length;d++){for(var h=c.slice(d),g=this.trie.commonPrefixSearch(h),s=0;s<g.length;s++){r=g[s].v,t=g[s].k;for(var u=this.token_info_dictionary.target_map[r],_=0;_<u.length;_++){var l=parseInt(u[_]);n=this.token_info_dictionary.dictionary.getShort(l),e=this.token_info_dictionary.dictionary.getShort(l+2),o=this.token_info_dictionary.dictionary.getShort(l+4),a.append(new ViterbiNode(l,o,d+1,t.length,"KNOWN",n,e,t))}}var y=new SurrogateAwareString(h),w=new SurrogateAwareString(y.charAt(0)),k=this.unknown_dictionary.lookup(w.toString());if(null==g||0===g.length||1===k.is_always_invoke){if(t=w,1===k.is_grouping&&1<y.length)for(var S=1;S<y.length;S++){var f=y.charAt(S),p=this.unknown_dictionary.lookup(f);if(k.class_name!==p.class_name)break;t+=f}for(var b=this.unknown_dictionary.target_map[k.class_id],v=0;v<b.length;v++){var V=parseInt(b[v]);n=this.unknown_dictionary.dictionary.getShort(V),e=this.unknown_dictionary.dictionary.getShort(V+2),o=this.unknown_dictionary.dictionary.getShort(V+4),a.append(new ViterbiNode(V,o,d+1,t.length,"UNKNOWN",n,e,t.toString()))}}}return a.appendEos(),a},module.exports=ViterbiBuilder;

},{"../util/SurrogateAwareString":20,"./ViterbiLattice":22,"./ViterbiNode":23}],22:[function(require,module,exports){
"use strict";var ViterbiNode=require("./ViterbiNode");function ViterbiLattice(){this.nodes_end_at=[],this.nodes_end_at[0]=[new ViterbiNode(-1,0,0,0,"BOS",0,0,"")],this.eos_pos=1}ViterbiLattice.prototype.append=function(t){var e=t.start_pos+t.length-1;this.eos_pos<e&&(this.eos_pos=e);var i=this.nodes_end_at[e];null==i&&(i=[]),i.push(t),this.nodes_end_at[e]=i},ViterbiLattice.prototype.appendEos=function(){var t=this.nodes_end_at.length;this.eos_pos++,this.nodes_end_at[t]=[new ViterbiNode(-1,0,this.eos_pos,0,"EOS",0,0,"")]},module.exports=ViterbiLattice;

},{"./ViterbiNode":23}],23:[function(require,module,exports){
"use strict";function ViterbiNode(t,s,i,e,h,r,o,u){this.name=t,this.cost=s,this.start_pos=i,this.length=e,this.left_id=r,this.right_id=o,this.prev=null,this.surface_form=u,this.shortest_cost="BOS"===h?0:Number.MAX_VALUE,this.type=h}module.exports=ViterbiNode;

},{}],24:[function(require,module,exports){
"use strict";function ViterbiSearcher(r){this.connection_costs=r}ViterbiSearcher.prototype.search=function(r){return r=this.forward(r),this.backward(r)},ViterbiSearcher.prototype.forward=function(r){var e,t,o;for(e=1;e<=r.eos_pos;e++){var n=r.nodes_end_at[e];if(null!=n)for(t=0;t<n.length;t++){var i,s=n[t],a=Number.MAX_VALUE,c=r.nodes_end_at[s.start_pos-1];if(null!=c){for(o=0;o<c.length;o++){var l,u=c[o];null==s.left_id||null==u.right_id?(console.log("Left or right is null"),l=0):l=this.connection_costs.get(u.right_id,s.left_id);var h=u.shortest_cost+l+s.cost;h<a&&(i=u,a=h)}s.prev=i,s.shortest_cost=a}}}return r},ViterbiSearcher.prototype.backward=function(r){var e=[],t=r.nodes_end_at[r.nodes_end_at.length-1][0].prev;if(null==t)return[];for(;"BOS"!==t.type;){if(e.push(t),null==t.prev)return[];t=t.prev}return e.reverse()},module.exports=ViterbiSearcher;

},{}],25:[function(require,module,exports){
(function (process){
function normalizeArray(r,t){for(var e=0,n=r.length-1;n>=0;n--){var o=r[n];"."===o?r.splice(n,1):".."===o?(r.splice(n,1),e++):e&&(r.splice(n,1),e--)}if(t)for(;e--;e)r.unshift("..");return r}function basename(r){"string"!=typeof r&&(r+="");var t,e=0,n=-1,o=!0;for(t=r.length-1;t>=0;--t)if(47===r.charCodeAt(t)){if(!o){e=t+1;break}}else-1===n&&(o=!1,n=t+1);return-1===n?"":r.slice(e,n)}function filter(r,t){if(r.filter)return r.filter(t);for(var e=[],n=0;n<r.length;n++)t(r[n],n,r)&&e.push(r[n]);return e}exports.resolve=function(){for(var r="",t=!1,e=arguments.length-1;e>=-1&&!t;e--){var n=e>=0?arguments[e]:process.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");n&&(r=n+"/"+r,t="/"===n.charAt(0))}return(t?"/":"")+(r=normalizeArray(filter(r.split("/"),function(r){return!!r}),!t).join("/"))||"."},exports.normalize=function(r){var t=exports.isAbsolute(r),e="/"===substr(r,-1);return(r=normalizeArray(filter(r.split("/"),function(r){return!!r}),!t).join("/"))||t||(r="."),r&&e&&(r+="/"),(t?"/":"")+r},exports.isAbsolute=function(r){return"/"===r.charAt(0)},exports.join=function(){var r=Array.prototype.slice.call(arguments,0);return exports.normalize(filter(r,function(r,t){if("string"!=typeof r)throw new TypeError("Arguments to path.join must be strings");return r}).join("/"))},exports.relative=function(r,t){function e(r){for(var t=0;t<r.length&&""===r[t];t++);for(var e=r.length-1;e>=0&&""===r[e];e--);return t>e?[]:r.slice(t,e-t+1)}r=exports.resolve(r).substr(1),t=exports.resolve(t).substr(1);for(var n=e(r.split("/")),o=e(t.split("/")),s=Math.min(n.length,o.length),i=s,u=0;u<s;u++)if(n[u]!==o[u]){i=u;break}var l=[];for(u=i;u<n.length;u++)l.push("..");return(l=l.concat(o.slice(i))).join("/")},exports.sep="/",exports.delimiter=":",exports.dirname=function(r){if("string"!=typeof r&&(r+=""),0===r.length)return".";for(var t=r.charCodeAt(0),e=47===t,n=-1,o=!0,s=r.length-1;s>=1;--s)if(47===(t=r.charCodeAt(s))){if(!o){n=s;break}}else o=!1;return-1===n?e?"/":".":e&&1===n?"/":r.slice(0,n)},exports.basename=function(r,t){var e=basename(r);return t&&e.substr(-1*t.length)===t&&(e=e.substr(0,e.length-t.length)),e},exports.extname=function(r){"string"!=typeof r&&(r+="");for(var t=-1,e=0,n=-1,o=!0,s=0,i=r.length-1;i>=0;--i){var u=r.charCodeAt(i);if(47!==u)-1===n&&(o=!1,n=i+1),46===u?-1===t?t=i:1!==s&&(s=1):-1!==t&&(s=-1);else if(!o){e=i+1;break}}return-1===t||-1===n||0===s||1===s&&t===n-1&&t===e+1?"":r.slice(t,n)};var substr="b"==="ab".substr(-1)?function(r,t,e){return r.substr(t,e)}:function(r,t,e){return t<0&&(t=r.length+t),r.substr(t,e)};

}).call(this,require('_process'))
},{"_process":26}],26:[function(require,module,exports){
var cachedSetTimeout,cachedClearTimeout,process=module.exports={};function defaultSetTimout(){throw new Error("setTimeout has not been defined")}function defaultClearTimeout(){throw new Error("clearTimeout has not been defined")}function runTimeout(e){if(cachedSetTimeout===setTimeout)return setTimeout(e,0);if((cachedSetTimeout===defaultSetTimout||!cachedSetTimeout)&&setTimeout)return cachedSetTimeout=setTimeout,setTimeout(e,0);try{return cachedSetTimeout(e,0)}catch(t){try{return cachedSetTimeout.call(null,e,0)}catch(t){return cachedSetTimeout.call(this,e,0)}}}function runClearTimeout(e){if(cachedClearTimeout===clearTimeout)return clearTimeout(e);if((cachedClearTimeout===defaultClearTimeout||!cachedClearTimeout)&&clearTimeout)return cachedClearTimeout=clearTimeout,clearTimeout(e);try{return cachedClearTimeout(e)}catch(t){try{return cachedClearTimeout.call(null,e)}catch(t){return cachedClearTimeout.call(this,e)}}}!function(){try{cachedSetTimeout="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){cachedSetTimeout=defaultSetTimout}try{cachedClearTimeout="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){cachedClearTimeout=defaultClearTimeout}}();var currentQueue,queue=[],draining=!1,queueIndex=-1;function cleanUpNextTick(){draining&&currentQueue&&(draining=!1,currentQueue.length?queue=currentQueue.concat(queue):queueIndex=-1,queue.length&&drainQueue())}function drainQueue(){if(!draining){var e=runTimeout(cleanUpNextTick);draining=!0;for(var t=queue.length;t;){for(currentQueue=queue,queue=[];++queueIndex<t;)currentQueue&&currentQueue[queueIndex].run();queueIndex=-1,t=queue.length}currentQueue=null,draining=!1,runClearTimeout(e)}}function Item(e,t){this.fun=e,this.array=t}function noop(){}process.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];queue.push(new Item(e,t)),1!==queue.length||draining||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},process.title="browser",process.browser=!0,process.env={},process.argv=[],process.version="",process.versions={},process.on=noop,process.addListener=noop,process.once=noop,process.off=noop,process.removeListener=noop,process.removeAllListeners=noop,process.emit=noop,process.prependListener=noop,process.prependOnceListener=noop,process.listeners=function(e){return[]},process.binding=function(e){throw new Error("process.binding is not supported")},process.cwd=function(){return"/"},process.chdir=function(e){throw new Error("process.chdir is not supported")},process.umask=function(){return 0};

},{}],27:[function(require,module,exports){
(function (setImmediate,clearImmediate){
var nextTick=require("process/browser.js").nextTick,apply=Function.prototype.apply,slice=Array.prototype.slice,immediateIds={},nextImmediateId=0;function Timeout(e,t){this._id=e,this._clearFn=t}exports.setTimeout=function(){return new Timeout(apply.call(setTimeout,window,arguments),clearTimeout)},exports.setInterval=function(){return new Timeout(apply.call(setInterval,window,arguments),clearInterval)},exports.clearTimeout=exports.clearInterval=function(e){e.close()},Timeout.prototype.unref=Timeout.prototype.ref=function(){},Timeout.prototype.close=function(){this._clearFn.call(window,this._id)},exports.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},exports.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},exports._unrefActive=exports.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},exports.setImmediate="function"==typeof setImmediate?setImmediate:function(e){var t=nextImmediateId++,i=!(arguments.length<2)&&slice.call(arguments,1);return immediateIds[t]=!0,nextTick(function(){immediateIds[t]&&(i?e.apply(null,i):e.call(null),exports.clearImmediate(t))}),t},exports.clearImmediate="function"==typeof clearImmediate?clearImmediate:function(e){delete immediateIds[e]};

}).call(this,require("timers").setImmediate,require("timers").clearImmediate)
},{"process/browser.js":26,"timers":27}],28:[function(require,module,exports){
(function(){"use strict";function t(t){throw t}var r=void 0,e=this;function i(t,i){var n,s=t.split("."),a=e;!(s[0]in a)&&a.execScript&&a.execScript("var "+s[0]);for(;s.length&&(n=s.shift());)s.length||i===r?a=a[n]?a[n]:a[n]={}:a[n]=i}var n,s="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView;for(new(s?Uint8Array:Array)(256),n=0;256>n;++n)for(var a=(a=n)>>>1;a;a>>>=1)0;function h(t,r,e){var i,n="number"==typeof r?r:r=0,s="number"==typeof e?e:t.length;for(i=-1,n=7&s;n--;++r)i=i>>>8^u[255&(i^t[r])];for(n=s>>3;n--;r+=8)i=(i=(i=(i=(i=(i=(i=(i=i>>>8^u[255&(i^t[r])])>>>8^u[255&(i^t[r+1])])>>>8^u[255&(i^t[r+2])])>>>8^u[255&(i^t[r+3])])>>>8^u[255&(i^t[r+4])])>>>8^u[255&(i^t[r+5])])>>>8^u[255&(i^t[r+6])])>>>8^u[255&(i^t[r+7])];return(4294967295^i)>>>0}var o=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],u=s?new Uint32Array(o):o;function f(){}function c(t){var r,e,i,n,a,h,o,u,f,c,l=t.length,p=0,y=Number.POSITIVE_INFINITY;for(u=0;u<l;++u)t[u]>p&&(p=t[u]),t[u]<y&&(y=t[u]);for(r=1<<p,e=new(s?Uint32Array:Array)(r),i=1,n=0,a=2;i<=p;){for(u=0;u<l;++u)if(t[u]===i){for(h=0,o=n,f=0;f<i;++f)h=h<<1|1&o,o>>=1;for(c=i<<16|u,f=h;f<r;f+=a)e[f]=c;++n}++i,n<<=1,a<<=1}return[e,p,y]}f.prototype.getName=function(){return this.name},f.prototype.getData=function(){return this.data},f.prototype.G=function(){return this.H};var l,p=[];for(l=0;288>l;l++)switch(!0){case 143>=l:p.push([l+48,8]);break;case 255>=l:p.push([l-144+400,9]);break;case 279>=l:p.push([l-256+0,7]);break;case 287>=l:p.push([l-280+192,8]);break;default:t("invalid literal: "+l)}var y=function(){function r(r){switch(!0){case 3===r:return[257,r-3,0];case 4===r:return[258,r-4,0];case 5===r:return[259,r-5,0];case 6===r:return[260,r-6,0];case 7===r:return[261,r-7,0];case 8===r:return[262,r-8,0];case 9===r:return[263,r-9,0];case 10===r:return[264,r-10,0];case 12>=r:return[265,r-11,1];case 14>=r:return[266,r-13,1];case 16>=r:return[267,r-15,1];case 18>=r:return[268,r-17,1];case 22>=r:return[269,r-19,2];case 26>=r:return[270,r-23,2];case 30>=r:return[271,r-27,2];case 34>=r:return[272,r-31,2];case 42>=r:return[273,r-35,3];case 50>=r:return[274,r-43,3];case 58>=r:return[275,r-51,3];case 66>=r:return[276,r-59,3];case 82>=r:return[277,r-67,4];case 98>=r:return[278,r-83,4];case 114>=r:return[279,r-99,4];case 130>=r:return[280,r-115,4];case 162>=r:return[281,r-131,5];case 194>=r:return[282,r-163,5];case 226>=r:return[283,r-195,5];case 257>=r:return[284,r-227,5];case 258===r:return[285,r-258,0];default:t("invalid length: "+r)}}var e,i,n=[];for(e=3;258>=e;e++)i=r(e),n[e]=i[2]<<24|i[1]<<16|i[0];return n}();function b(r,e){switch(this.i=[],this.j=32768,this.d=this.f=this.c=this.n=0,this.input=s?new Uint8Array(r):r,this.o=!1,this.k=d,this.w=!1,!e&&(e={})||(e.index&&(this.c=e.index),e.bufferSize&&(this.j=e.bufferSize),e.bufferType&&(this.k=e.bufferType),e.resize&&(this.w=e.resize)),this.k){case g:this.a=32768,this.b=new(s?Uint8Array:Array)(32768+this.j+258);break;case d:this.a=0,this.b=new(s?Uint8Array:Array)(this.j),this.e=this.D,this.q=this.A,this.l=this.C;break;default:t(Error("invalid inflate mode"))}}s&&new Uint32Array(y);var g=0,d=1;b.prototype.g=function(){for(;!this.o;){var e=T(this,3);switch(1&e&&(this.o=!0),e>>>=1){case 0:var i=this.input,n=this.c,a=this.b,h=this.a,o=i.length,u=r,f=a.length,l=r;switch(this.d=this.f=0,n+1>=o&&t(Error("invalid uncompressed block header: LEN")),u=i[n++]|i[n++]<<8,n+1>=o&&t(Error("invalid uncompressed block header: NLEN")),u===~(i[n++]|i[n++]<<8)&&t(Error("invalid uncompressed block header: length verify")),n+u>i.length&&t(Error("input buffer is broken")),this.k){case g:for(;h+u>a.length;){if(u-=l=f-h,s)a.set(i.subarray(n,n+l),h),h+=l,n+=l;else for(;l--;)a[h++]=i[n++];this.a=h,a=this.e(),h=this.a}break;case d:for(;h+u>a.length;)a=this.e({t:2});break;default:t(Error("invalid inflate mode"))}if(s)a.set(i.subarray(n,n+u),h),h+=u,n+=u;else for(;u--;)a[h++]=i[n++];this.c=n,this.a=h,this.b=a;break;case 1:this.l(M,j);break;case 2:var p,y,b,v,w=T(this,5)+257,A=T(this,5)+1,k=T(this,4)+4,U=new(s?Uint8Array:Array)(m.length),E=r,z=r,N=r,S=r,G=r;for(G=0;G<k;++G)U[m[G]]=T(this,3);if(!s)for(G=k,k=U.length;G<k;++G)U[m[G]]=0;for(p=c(U),E=new(s?Uint8Array:Array)(w+A),G=0,v=w+A;G<v;)switch(z=q(this,p),z){case 16:for(S=3+T(this,2);S--;)E[G++]=N;break;case 17:for(S=3+T(this,3);S--;)E[G++]=0;N=0;break;case 18:for(S=11+T(this,7);S--;)E[G++]=0;N=0;break;default:N=E[G++]=z}y=c(s?E.subarray(0,w):E.slice(0,w)),b=c(s?E.subarray(w):E.slice(w)),this.l(y,b);break;default:t(Error("unknown BTYPE: "+e))}}return this.q()};var v,w,A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],m=s?new Uint16Array(A):A,k=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],U=s?new Uint16Array(k):k,E=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],z=s?new Uint8Array(E):E,N=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],S=s?new Uint16Array(N):N,G=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],C=s?new Uint8Array(G):G,x=new(s?Uint8Array:Array)(288);for(v=0,w=x.length;v<w;++v)x[v]=143>=v?8:255>=v?9:279>=v?7:8;var D,I,M=c(x),Z=new(s?Uint8Array:Array)(30);for(D=0,I=Z.length;D<I;++D)Z[D]=5;var j=c(Z);function T(r,e){for(var i,n=r.f,s=r.d,a=r.input,h=r.c,o=a.length;s<e;)h>=o&&t(Error("input buffer is broken")),n|=a[h++]<<s,s+=8;return i=n&(1<<e)-1,r.f=n>>>e,r.d=s-e,r.c=h,i}function q(r,e){for(var i,n,s=r.f,a=r.d,h=r.input,o=r.c,u=h.length,f=e[0],c=e[1];a<c&&!(o>=u);)s|=h[o++]<<a,a+=8;return(n=(i=f[s&(1<<c)-1])>>>16)>a&&t(Error("invalid code length: "+n)),r.f=s>>n,r.d=a-n,r.c=o,65535&i}function B(t){this.input=t,this.c=0,this.m=[],this.s=!1}b.prototype.l=function(t,r){var e=this.b,i=this.a;this.r=t;for(var n,s,a,h,o=e.length-258;256!==(n=q(this,t));)if(256>n)i>=o&&(this.a=i,e=this.e(),i=this.a),e[i++]=n;else for(h=U[s=n-257],0<z[s]&&(h+=T(this,z[s])),n=q(this,r),a=S[n],0<C[n]&&(a+=T(this,C[n])),i>=o&&(this.a=i,e=this.e(),i=this.a);h--;)e[i]=e[i++-a];for(;8<=this.d;)this.d-=8,this.c--;this.a=i},b.prototype.C=function(t,r){var e=this.b,i=this.a;this.r=t;for(var n,s,a,h,o=e.length;256!==(n=q(this,t));)if(256>n)i>=o&&(o=(e=this.e()).length),e[i++]=n;else for(h=U[s=n-257],0<z[s]&&(h+=T(this,z[s])),n=q(this,r),a=S[n],0<C[n]&&(a+=T(this,C[n])),i+h>o&&(o=(e=this.e()).length);h--;)e[i]=e[i++-a];for(;8<=this.d;)this.d-=8,this.c--;this.a=i},b.prototype.e=function(){var t,r,e=new(s?Uint8Array:Array)(this.a-32768),i=this.a-32768,n=this.b;if(s)e.set(n.subarray(32768,e.length));else for(t=0,r=e.length;t<r;++t)e[t]=n[t+32768];if(this.i.push(e),this.n+=e.length,s)n.set(n.subarray(i,i+32768));else for(t=0;32768>t;++t)n[t]=n[i+t];return this.a=32768,n},b.prototype.D=function(t){var r,e,i,n=this.input.length/this.c+1|0,a=this.input,h=this.b;return t&&("number"==typeof t.t&&(n=t.t),"number"==typeof t.z&&(n+=t.z)),2>n?e=(i=(a.length-this.c)/this.r[2]/2*258|0)<h.length?h.length+i:h.length<<1:e=h.length*n,s?(r=new Uint8Array(e)).set(h):r=h,this.b=r},b.prototype.q=function(){var t,r,e,i,n,a=0,h=this.b,o=this.i,u=new(s?Uint8Array:Array)(this.n+(this.a-32768));if(0===o.length)return s?this.b.subarray(32768,this.a):this.b.slice(32768,this.a);for(r=0,e=o.length;r<e;++r)for(i=0,n=(t=o[r]).length;i<n;++i)u[a++]=t[i];for(r=32768,e=this.a;r<e;++r)u[a++]=h[r];return this.i=[],this.buffer=u},b.prototype.A=function(){var t,r=this.a;return s?this.w?(t=new Uint8Array(r)).set(this.b.subarray(0,r)):t=this.b.subarray(0,r):(this.b.length>r&&(this.b.length=r),t=this.b),this.buffer=t},B.prototype.F=function(){return this.s||this.g(),this.m.slice()},B.prototype.g=function(){for(var e=this.input.length;this.c<e;){var i,n,a=new f,o=r,u=r,c=r,l=r,p=r,y=r,g=r,d=this.input,v=this.c;switch(a.u=d[v++],a.v=d[v++],(31!==a.u||139!==a.v)&&t(Error("invalid file signature:"+a.u+","+a.v)),a.p=d[v++],a.p){case 8:break;default:t(Error("unknown compression method: "+a.p))}if(a.h=d[v++],n=d[v++]|d[v++]<<8|d[v++]<<16|d[v++]<<24,a.H=new Date(1e3*n),a.N=d[v++],a.M=d[v++],0<(4&a.h)&&(a.I=d[v++]|d[v++]<<8,v+=a.I),0<(8&a.h)){for(y=[],p=0;0<(l=d[v++]);)y[p++]=String.fromCharCode(l);a.name=y.join("")}if(0<(16&a.h)){for(y=[],p=0;0<(l=d[v++]);)y[p++]=String.fromCharCode(l);a.J=y.join("")}0<(2&a.h)&&(a.B=65535&h(d,0,v),a.B!==(d[v++]|d[v++]<<8)&&t(Error("invalid header crc16"))),o=d[d.length-4]|d[d.length-3]<<8|d[d.length-2]<<16|d[d.length-1]<<24,d.length-v-4-4<512*o&&(c=o),u=new b(d,{index:v,bufferSize:c}),a.data=i=u.g(),v=u.c,a.K=g=(d[v++]|d[v++]<<8|d[v++]<<16|d[v++]<<24)>>>0,h(i,r,r)!==g&&t(Error("invalid CRC-32 checksum: 0x"+h(i,r,r).toString(16)+" / 0x"+g.toString(16))),a.L=o=(d[v++]|d[v++]<<8|d[v++]<<16|d[v++]<<24)>>>0,(4294967295&i.length)!==o&&t(Error("invalid input size: "+(4294967295&i.length)+" / "+o)),this.m.push(a),this.c=v}this.s=!0;var w,A,m,k=this.m,U=0,E=0;for(w=0,A=k.length;w<A;++w)E+=k[w].data.length;if(s)for(m=new Uint8Array(E),w=0;w<A;++w)m.set(k[w].data,U),U+=k[w].data.length;else{for(m=[],w=0;w<A;++w)m[w]=k[w].data;m=Array.prototype.concat.apply([],m)}return m},i("Zlib.Gunzip",B),i("Zlib.Gunzip.prototype.decompress",B.prototype.g),i("Zlib.Gunzip.prototype.getMembers",B.prototype.F),i("Zlib.GunzipMember",f),i("Zlib.GunzipMember.prototype.getName",f.prototype.getName),i("Zlib.GunzipMember.prototype.getData",f.prototype.getData),i("Zlib.GunzipMember.prototype.getMtime",f.prototype.G)}).call(this);

},{}],29:[function(require,module,exports){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var _createClass=function(){function e(e,r){for(var o=0;o<r.length;o++){var t=r[o];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}return function(r,o,t){return o&&e(r.prototype,o),t&&e(r,t),r}}(),_kuromoji=require("kuromoji"),_kuromoji2=_interopRequireDefault(_kuromoji);function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classCallCheck(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}var isNode=!1,isBrowser="undefined"!=typeof window;!isBrowser&&"undefined"!=typeof module&&module.exports&&(isNode=!0);var Analyzer=function(){function e(){var r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).dictPath;_classCallCheck(this,e),this._analyzer=null,this._dictPath=r||(isNode?require.resolve("kuromoji").replace(/src(?!.*src).*/,"dict/"):"node_modules/kuromoji/dict/")}return _createClass(e,[{key:"init",value:function(){var e=this;return new Promise(function(r,o){var t=e;null==e._analyzer?_kuromoji2.default.builder({dicPath:e._dictPath}).build(function(e,i){if(e)return o(e);t._analyzer=i,r()}):o(new Error("This analyzer has already been initialized."))})}},{key:"parse",value:function(){var e=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new Promise(function(o,t){if(""===r.trim())return o([]);for(var i=e._analyzer.tokenize(r),n=0;n<i.length;n++)i[n].verbose={},i[n].verbose.word_id=i[n].word_id,i[n].verbose.word_type=i[n].word_type,i[n].verbose.word_position=i[n].word_position,delete i[n].word_id,delete i[n].word_type,delete i[n].word_position;o(i)})}}]),e}();exports.default=Analyzer,module.exports=exports.default;

},{"kuromoji":15}]},{},[29])(29)
});